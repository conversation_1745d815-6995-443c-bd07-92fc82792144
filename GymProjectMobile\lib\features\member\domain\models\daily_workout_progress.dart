/// Günlük antrenman ilerleme modeli
class DailyWorkoutProgress {
  final DateTime workoutDate;
  final int totalExercises;
  final int completedExercises;
  final double completionPercentage;
  final int totalSets;
  final int completedSets;

  DailyWorkoutProgress({
    required this.workoutDate,
    required this.totalExercises,
    required this.completedExercises,
    required this.completionPercentage,
    required this.totalSets,
    required this.completedSets,
  });

  factory DailyWorkoutProgress.fromJson(Map<String, dynamic> json) {
    return DailyWorkoutProgress(
      workoutDate: DateTime.parse(json['workoutDate']),
      totalExercises: json['totalExercises'] ?? 0,
      completedExercises: json['completedExercises'] ?? 0,
      completionPercentage: (json['completionPercentage'] ?? 0.0).toDouble(),
      totalSets: json['totalSets'] ?? 0,
      completedSets: json['completedSets'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'workoutDate': workoutDate.toIso8601String(),
      'totalExercises': totalExercises,
      'completedExercises': completedExercises,
      'completionPercentage': completionPercentage,
      'totalSets': totalSets,
      'completedSets': completedSets,
    };
  }

  DailyWorkoutProgress copyWith({
    DateTime? workoutDate,
    int? totalExercises,
    int? completedExercises,
    double? completionPercentage,
    int? totalSets,
    int? completedSets,
  }) {
    return DailyWorkoutProgress(
      workoutDate: workoutDate ?? this.workoutDate,
      totalExercises: totalExercises ?? this.totalExercises,
      completedExercises: completedExercises ?? this.completedExercises,
      completionPercentage: completionPercentage ?? this.completionPercentage,
      totalSets: totalSets ?? this.totalSets,
      completedSets: completedSets ?? this.completedSets,
    );
  }

  /// İlerleme yüzdesini renkli gösterim için kategorize eder
  ProgressCategory get progressCategory {
    if (completionPercentage >= 80) return ProgressCategory.excellent;
    if (completionPercentage >= 60) return ProgressCategory.good;
    if (completionPercentage >= 40) return ProgressCategory.average;
    if (completionPercentage > 0) return ProgressCategory.poor;
    return ProgressCategory.none;
  }

  /// Kalan egzersiz sayısı
  int get remainingExercises => totalExercises - completedExercises;

  /// Kalan set sayısı
  int get remainingSets => totalSets - completedSets;

  /// Antrenman tamamlandı mı?
  bool get isWorkoutCompleted => completedExercises >= totalExercises;

  @override
  String toString() {
    return 'DailyWorkoutProgress(workoutDate: $workoutDate, totalExercises: $totalExercises, completedExercises: $completedExercises, completionPercentage: $completionPercentage, totalSets: $totalSets, completedSets: $completedSets)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is DailyWorkoutProgress &&
        other.workoutDate == workoutDate &&
        other.totalExercises == totalExercises &&
        other.completedExercises == completedExercises &&
        other.completionPercentage == completionPercentage &&
        other.totalSets == totalSets &&
        other.completedSets == completedSets;
  }

  @override
  int get hashCode {
    return workoutDate.hashCode ^
        totalExercises.hashCode ^
        completedExercises.hashCode ^
        completionPercentage.hashCode ^
        totalSets.hashCode ^
        completedSets.hashCode;
  }
}

/// İlerleme kategorileri
enum ProgressCategory {
  none,      // %0
  poor,      // %1-39
  average,   // %40-59
  good,      // %60-79
  excellent, // %80-100
}

/// İlerleme kategorisi uzantıları
extension ProgressCategoryExtension on ProgressCategory {
  String get displayName {
    switch (this) {
      case ProgressCategory.none:
        return 'Başlanmadı';
      case ProgressCategory.poor:
        return 'Zayıf';
      case ProgressCategory.average:
        return 'Orta';
      case ProgressCategory.good:
        return 'İyi';
      case ProgressCategory.excellent:
        return 'Mükemmel';
    }
  }

  String get emoji {
    switch (this) {
      case ProgressCategory.none:
        return '⚪';
      case ProgressCategory.poor:
        return '🔴';
      case ProgressCategory.average:
        return '🟡';
      case ProgressCategory.good:
        return '🟢';
      case ProgressCategory.excellent:
        return '🏆';
    }
  }
}
