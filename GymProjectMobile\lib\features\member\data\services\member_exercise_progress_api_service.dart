import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../../../core/constants/api_constants.dart';
import '../../../../core/services/auth_service.dart';
import '../../../../core/models/api_response.dart';
import '../../domain/models/exercise_completion_status.dart';
import '../../domain/models/daily_workout_progress.dart';
import '../../domain/models/member_exercise_progress_add.dart';

/// Üye egzersiz ilerleme takip API servisi
/// Performance optimized for 10.000+ users
class MemberExerciseProgressApiService {
  final AuthService _authService;

  MemberExerciseProgressApiService(this._authService);

  /// Mobil API için - User ID'ye göre günlük egzersiz tamamlama durumunu getirir
  Future<ApiResponse<List<ExerciseCompletionStatus>>> getExerciseCompletionStatusByUser({
    DateTime? date,
  }) async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        return ApiResponse.error(message: 'Token bulunamadı');
      }

      final targetDate = date ?? DateTime.now();
      final dateParam = targetDate.toIso8601String().split('T')[0];

      final response = await http.get(
        Uri.parse('${ApiConstants.baseUrl}/api/MemberExerciseProgress/getexercisecompletionstatusbyuser?date=$dateParam'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        
        if (jsonData['success'] == true) {
          final List<dynamic> dataList = jsonData['data'] ?? [];
          final statuses = dataList
              .map((item) => ExerciseCompletionStatus.fromJson(item))
              .toList();
          
          return ApiResponse.success(data: statuses, message: jsonData['message']);
        } else {
          return ApiResponse.error(message: jsonData['message'] ?? 'Bilinmeyen hata');
        }
      } else if (response.statusCode == 401) {
        return ApiResponse.error(message: 'Oturum süresi dolmuş');
      } else {
        return ApiResponse.error(message: 'Sunucu hatası: ${response.statusCode}');
      }
    } catch (e) {
      return ApiResponse.error(message: 'Bağlantı hatası: $e');
    }
  }

  /// Mobil API için - User ID'ye göre günlük antrenman ilerlemesini getirir
  Future<ApiResponse<DailyWorkoutProgress>> getDailyWorkoutProgressByUser({
    DateTime? date,
  }) async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        return ApiResponse.error(message: 'Token bulunamadı');
      }

      final targetDate = date ?? DateTime.now();
      final dateParam = targetDate.toIso8601String().split('T')[0];

      final response = await http.get(
        Uri.parse('${ApiConstants.baseUrl}/api/MemberExerciseProgress/getdailyworkoutprogressbyuser?date=$dateParam'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        
        if (jsonData['success'] == true) {
          final progress = DailyWorkoutProgress.fromJson(jsonData['data']);
          return ApiResponse.success(data: progress, message: jsonData['message']);
        } else {
          return ApiResponse.error(message: jsonData['message'] ?? 'Bilinmeyen hata');
        }
      } else if (response.statusCode == 401) {
        return ApiResponse.error(message: 'Oturum süresi dolmuş');
      } else {
        return ApiResponse.error(message: 'Sunucu hatası: ${response.statusCode}');
      }
    } catch (e) {
      return ApiResponse.error(message: 'Bağlantı hatası: $e');
    }
  }

  /// Mobil API için - User ID'ye göre egzersiz tamamlama kaydı ekler
  Future<ApiResponse<bool>> addProgressByUser(MemberExerciseProgressAdd progressAdd) async {
    try {
      final token = await _authService.getToken();
      if (token == null) {
        return ApiResponse.error(message: 'Token bulunamadı');
      }

      final response = await http.post(
        Uri.parse('${ApiConstants.baseUrl}/api/MemberExerciseProgress/addprogressbyuser'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode(progressAdd.toJson()),
      );

      if (response.statusCode == 200) {
        final jsonData = json.decode(response.body);
        
        if (jsonData['success'] == true) {
          return ApiResponse.success(data: true, message: jsonData['message']);
        } else {
          return ApiResponse.error(message: jsonData['message'] ?? 'Bilinmeyen hata');
        }
      } else if (response.statusCode == 401) {
        return ApiResponse.error(message: 'Oturum süresi dolmuş');
      } else if (response.statusCode == 400) {
        final jsonData = json.decode(response.body);
        return ApiResponse.error(message: jsonData['message'] ?? 'Geçersiz istek');
      } else {
        return ApiResponse.error(message: 'Sunucu hatası: ${response.statusCode}');
      }
    } catch (e) {
      return ApiResponse.error(message: 'Bağlantı hatası: $e');
    }
  }

  /// Rate limiting kontrolü için son işlem zamanını kontrol eder
  DateTime? _lastRequestTime;
  
  bool canMakeRequest() {
    final now = DateTime.now();
    if (_lastRequestTime == null) {
      _lastRequestTime = now;
      return true;
    }
    
    final timeDiff = now.difference(_lastRequestTime!).inSeconds;
    if (timeDiff >= 1) {
      _lastRequestTime = now;
      return true;
    }
    
    return false;
  }

  /// Spam önlemeli egzersiz tamamlama
  Future<ApiResponse<bool>> addProgressWithRateLimit(MemberExerciseProgressAdd progressAdd) async {
    if (!canMakeRequest()) {
      return ApiResponse.error(message: 'Çok hızlı işlem yapıyorsunuz. Lütfen 1 saniye bekleyin.');
    }
    
    return await addProgressByUser(progressAdd);
  }
}
