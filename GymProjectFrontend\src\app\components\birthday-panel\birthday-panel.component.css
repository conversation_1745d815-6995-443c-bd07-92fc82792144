.birthday-panel-container {
  padding: 20px;
}

.dialog-content {
  margin-bottom: 20px;
}

.no-birthdays {
  text-align: center;
  padding: 30px;
  color: var(--text-secondary, #666);
}

.message-template {
  background-color: var(--bg-secondary, #f8f9fa);
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid var(--border-color, #dee2e6);
}

.members-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.member-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: var(--card-bg-color, #fff);
  color: var(--text-color, #212529);
  border-radius: 8px;
  box-shadow: 0 2px 4px var(--shadow-color, rgba(0, 0, 0, 0.1));
  border: 1px solid var(--border-color, #dee2e6);
}

.member-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.avatar-circle {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--primary-color, #4361ee);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: bold;
}

.member-details h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color, #212529);
}

.member-details p {
  margin: 5px 0 0;
  font-size: 14px;
  color: var(--text-secondary, #666);
}

.days-left {
  color: var(--primary-color, #4361ee) !important;
  font-weight: 600;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

/* Styles for the standalone page version */
.birthday-content {
  padding: 15px;
}

/* Dark mode specific overrides */
[data-theme="dark"] .member-card {
  border: 1px solid var(--border-color, #343a40);
}

[data-theme="dark"] .message-template {
  background-color: var(--bg-tertiary, #2d2d2d);
}

[data-theme="dark"] .member-details h4,
[data-theme="dark"] .member-details p {
  color: var(--text-color, #e9ecef);
}

[data-theme="dark"] .days-left {
  color: var(--accent-color, #4895ef) !important;
}

[data-theme="dark"] .no-birthdays {
  color: var(--text-muted, #adb5bd);
}

/* Fix for textarea placeholder in dark mode */
[data-theme="dark"] textarea::placeholder {
  color: var(--text-muted, #adb5bd);
  opacity: 0.7;
}

[data-theme="dark"] textarea {
  color: var(--text-color, #e9ecef);
  background-color: var(--bg-tertiary, #2d2d2d);
}

@media (max-width: 768px) {
  .member-card {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .member-actions {
    margin-top: 15px;
    width: 100%;
  }
  
  .member-actions button {
    width: 100%;
  }
}
