cd qr_turnstile_new
source venv/bin/activate
python qr_turnstile_control.py

yukarıdaki kod raspberry yi baştan başlatıp programı çalıştırma kodu aşağıdaki kodda yukarıdakinin oluşturulma yolu

sanal ortam oluşturmak için adım adım talimatlar verebilirim. İşte yapmanız gerekenler:

1. Raspberry Pi'nizi güncelleyin:
   ```
   sudo apt update
   sudo apt upgrade -y
   ```

2. Python3-venv paketini kurun:
   ```
   sudo apt install python3-venv
   ```

3. Yeni bir proje klasörü oluşturun:
   ```
   mkdir qr_turnstile_new
   cd qr_turnstile_new
   ```

4. Bu klasörde yeni bir sanal ortam oluşturun:
   ```
   python3 -m venv venv
   ```

5. Sanal ortamı aktifleştirin:
   ```
   source venv/bin/activate
   ```

6. <PERSON>ere<PERSON><PERSON> kütüphaneleri kurun:
   ```
   pip install pillow requests RPi.GPIO
   ```

7. ZBar kütüphanesini sistem düzeyinde kurun:
   ```
   sudo apt install libzbar0
   ```

8. Proje dosyanızı oluşturun:
   ```
   nano qr_turnstile_control.py
   ```

9. içine yazılacak kod

import time
import tkinter as tk
from tkinter import ttk
import requests
import json
import RPi.GPIO as GPIO
import threading
from datetime import datetime
from PIL import Image, ImageTk
import os
import locale
import pyautogui

# Türkçe tarih formatı için locale ayarı
locale.setlocale(locale.LC_TIME, 'tr_TR.UTF-8')

# API endpoint
API_URL = "https://api.gymkod.com/api/Member/scannumber"

# Turnike kontrol ayarları
# Not: GPIO pinleri şu anda kullanılmıyor, turnike bağlantısı yapıldığında aktifleştirilecek
TURNSTILE_ENABLED = False  # Turnike kontrolü aktif mi?
TURNSTILE_PIN = 18  # Turnike için kullanılacak GPIO pin numarası (bağlantı yapıldığında kullanılacak)

# GPIO setup - şu anda devre dışı
if TURNSTILE_ENABLED:
    GPIO.setmode(GPIO.BCM)
    GPIO.setup(TURNSTILE_PIN, GPIO.OUT)

class QRTurnstileApp:
    def __init__(self, master):
        self.master = master
        master.title("QR Turnstile Control")
        master.geometry("800x480")  # Raspberry Pi 7" ekran boyutu
        master.configure(bg='white')  # Genel arka plan rengini beyaz yap

        # Tam ekran moduna geçiş
        master.attributes('-fullscreen', True)
        master.bind('<Escape>', lambda e: master.attributes('-fullscreen', False))

        self.style = ttk.Style()
        self.style.theme_use('clam')  # Modern görünüm için tema seç
        self.style.configure('TFrame', background='white')
        self.style.configure('TLabel', background='white', font=('Helvetica', 14))
        self.style.configure('TButton', font=('Helvetica', 12))

        self.frame = ttk.Frame(master)
        self.frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Saat ve tarih göstergesi (sağ üstte)
        self.datetime_label = ttk.Label(self.frame, font=("Helvetica", 26, "bold"), foreground="#333333")
        self.datetime_label.pack(side=tk.TOP, anchor=tk.NE, padx=20, pady=10)

        # Logo yükleme (eğer varsa)
        self.load_logo()

        # QR Kodunu Okutun yazısı
        self.qr_label = ttk.Label(self.frame, text="QR Kodunu Okutunuz", font=("Helvetica", 36, "italic","bold"), foreground="#0056A0")
        self.qr_label.pack(pady=20)

        # Gizli giriş alanı
        self.qr_entry = ttk.Entry(self.frame, font=("Helvetica", 24))
        self.qr_entry.place(x=-1000, y=-1000)

        self.result_frame = ttk.Frame(self.frame, style='TFrame')
        self.result_frame.pack(expand=True, fill='both', pady=20)

        self.status_label = ttk.Label(self.frame, text="", font=("Helvetica", 24, "bold"))
        self.status_label.pack(side=tk.BOTTOM, pady=20)

        self.processing_lock = threading.Lock()
        self.is_processing = False

        self.master.after(100, self.focus_qr_entry)
        self.update_datetime()
        self.move_cursor_to_top_right()

    def load_logo(self):
        logo_path = "logo.png"  # Logo dosya yolunu güncelleyin
        if os.path.exists(logo_path):
            try:
                self.logo = Image.open(logo_path)

                # Ekran boyutlarını al
                screen_width = self.master.winfo_screenwidth()
                screen_height = self.master.winfo_screenheight()

                # Logo için maksimum boyutları belirle (ekranın %30'u)
                max_width = int(screen_width * 0.7)
                max_height = int(screen_height * 0.4)

                # Orijinal logo boyutlarını al
                logo_width, logo_height = self.logo.size

                # En-boy oranını koru
                aspect_ratio = logo_width / logo_height

                if logo_width > max_width or logo_height > max_height:
                    if logo_width / max_width > logo_height / max_height:
                        new_width = max_width
                        new_height = int(new_width / aspect_ratio)
                    else:
                        new_height = max_height
                        new_width = int(new_height * aspect_ratio)
                else:
                    new_width, new_height = logo_width, logo_height

                self.logo = self.logo.resize((new_width, new_height), Image.LANCZOS)
                self.logo_tk = ImageTk.PhotoImage(self.logo)
                self.logo_label = ttk.Label(self.frame, image=self.logo_tk, background='white')
                self.logo_label.pack(pady=(0, 20))
            except Exception as e:
                print(f"Logo yüklenirken hata oluştu: {e}")
        else:
            print(f"Logo dosyası bulunamadı: {logo_path}")

    def focus_qr_entry(self):
        self.qr_entry.focus_set()

    def open_turnstile(self):
        """Turnike açma fonksiyonu"""
        if TURNSTILE_ENABLED:
            # Turnike bağlantısı yapıldığında bu kısım aktifleştirilecek
            GPIO.output(TURNSTILE_PIN, GPIO.HIGH)

        # Turnike açıldı mesajı
        print("Turnike açılıyor...")
        self.status_label.config(text="TURNİKE AÇILIYOR...", foreground="#32CD32")

        # 5 saniye sonra turnikeyi kapat
        self.master.after(5000, self.close_turnstile)

    def close_turnstile(self):
        """Turnike kapatma fonksiyonu"""
        if TURNSTILE_ENABLED:
            # Turnike bağlantısı yapıldığında bu kısım aktifleştirilecek
            GPIO.output(TURNSTILE_PIN, GPIO.LOW)

        # Turnike kapandı mesajı
        print("Turnike kapandı.")
        self.status_label.config(text="QR KODUNU OKUTUNUZ", foreground="#0056A0")

    def get_member_data(self, qr_data):
        try:
            response = requests.post(API_URL, json={"scannumber": qr_data}, timeout=5)
            data = response.json()
            return data
        except requests.exceptions.RequestException:
            return {"success": False, "message": "Bağlantı hatası. Lütfen tekrar deneyin."}
        except json.JSONDecodeError:
            return {"success": False, "message": "Geçersiz cevap alındı. Lütfen tekrar deneyin."}

    def process_qr(self, event=None):
        if self.is_processing:
            self.status_label.config(text="Lütfen bekleyiniz...", foreground="#FF8C00")
            return

        qr_data = self.qr_entry.get()
        self.qr_entry.delete(0, tk.END)
        self.qr_entry.config(state='disabled')

        with self.processing_lock:
            self.is_processing = True
            self.status_label.config(text="İşlem yapılıyor...", foreground="#0056A0")

            for widget in self.result_frame.winfo_children():
                widget.destroy()

            data = self.get_member_data(qr_data)

            if data.get("success") and data.get("data"):
                member_data = data["data"]
                memberships = member_data.get('memberships', [])
                member_name = member_data.get('memberName', '')
                welcome_message = member_data.get('message', '').upper()

                # Üyelikleri branşlara göre grupla ve günleri topla
                branch_totals = {}
                future_memberships = {}  # İleri tarihli üyelikler için
                current_date = datetime.now()

                for membership in memberships:
                    branch = membership.get('branch', 'Bilinmiyor')
                    remaining_days = membership.get('remainingDays', 0)
                    start_date_str = membership.get('startDate', '')

                    # Start date'i datetime objesine çevir
                    try:
                        start_date = datetime.strptime(start_date_str.split('T')[0], '%Y-%m-%d')

                        # Eğer başlangıç tarihi gelecekte ise
                        if start_date > current_date:
                            if branch not in future_memberships:
                                future_memberships[branch] = start_date
                            continue  # Bu üyeliği toplama dahil etme

                    except (ValueError, AttributeError):
                        pass  # Tarih parse edilemezse geç

                    # Aktif üyelikleri topla
                    if branch in branch_totals:
                        branch_totals[branch] += remaining_days
                    else:
                        branch_totals[branch] = remaining_days

                has_active_membership = any(days > 0 for days in branch_totals.values())
                has_future_membership = bool(future_memberships)

                # Eğer aktif veya ileri tarihli üyelik yoksa merhaba mesajını ve API mesajını göster
                if not has_active_membership and not has_future_membership:
                    ttk.Label(self.result_frame,
                            text=f"MERHABA {member_name}",
                            font=("Helvetica", 28, "bold"),
                            foreground="#2E8B57").pack(pady=10)
                    ttk.Label(self.result_frame,
                            text=member_data.get('message', '').upper(),
                            font=("Helvetica", 24, "bold"),
                            foreground="#FF4500").pack(pady=5)

                if has_active_membership:
                    # Hoşgeldiniz mesajını göster
                    ttk.Label(self.result_frame,
                            text=welcome_message,
                            font=("Helvetica", 28, "bold"),
                            foreground="#2E8B57").pack(pady=10)

                    # Aktif üyelikleri göster
                    sorted_branches = sorted(branch_totals.items())
                    for branch, total_days in sorted_branches:
                        if total_days > 0:
                            ttk.Label(self.result_frame,
                                    text=f"{branch.upper()} - KALAN GÜN: {total_days}",
                                    font=("Helvetica", 24, "bold"),
                                    foreground="#4682B4").pack(pady=5)

                # İleri tarihli üyelikleri göster
                if future_memberships:
                    ttk.Label(self.result_frame,
                            text=f"MERHABA {member_name}",
                            font=("Helvetica", 28, "bold"),
                            foreground="#2E8B57").pack(pady=10)

                for branch, start_date in future_memberships.items():
                    formatted_date = start_date.strftime('%d.%m.%Y')
                    ttk.Label(self.result_frame,
                            text=f"{branch.upper()} ÜYELİĞİNİZİN BAŞLAMA TARİHİ: {formatted_date}",
                            font=("Helvetica", 24, "bold"),
                            foreground="#FF8C00").pack(pady=5)

                if has_active_membership:
                    self.status_label.config(text="TURNİKE AÇILIYOR...", foreground="#32CD32")
                    self.open_turnstile()
                else:
                    self.status_label.config(text="TURNİKE AÇILMIYOR.", foreground="#FF4500")
            else:
                message = data.get("message", "Bilinmeyen hata")
                ttk.Label(self.result_frame,
                        text=message.upper(),
                        font=("Helvetica", 28, "bold"),
                        foreground="#FF4500").pack(expand=True)

            self.master.after(4000, self.reset_display)

    def update_datetime(self):
        now = datetime.now()
        date_str = now.strftime("%d %B %Y %A")  # Örnek: 30 Temmuz 2024 Salı
        time_str = now.strftime("%H:%M:%S")
        self.datetime_label.config(text=f"{date_str} {time_str}")
        self.master.after(1000, self.update_datetime)

    def reset_display(self):
        for widget in self.result_frame.winfo_children():
            widget.destroy()
        self.status_label.config(text="", foreground="black")
        self.qr_entry.config(state='normal')
        self.qr_entry.focus_set()
        self.is_processing = False

    def move_cursor_to_top_right(self):
        screen_width, screen_height = pyautogui.size()
        pyautogui.moveTo(screen_width - 10, 10)

def main():
    root = tk.Tk()
    app = QRTurnstileApp(root)
    root.bind('<Return>', app.process_qr)
    root.mainloop()

if __name__ == "__main__":
    try:
        main()
    finally:
        GPIO.cleanup()


10. Kodu çalıştırın:
    ```
    python qr_turnstile_control.py
    ```

11. İşiniz bittiğinde, sanal ortamdan çıkmak için:
    ```
    deactivate
    ```
