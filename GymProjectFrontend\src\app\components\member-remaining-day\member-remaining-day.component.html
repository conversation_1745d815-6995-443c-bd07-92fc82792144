<div class="container-fluid mt-4 fade-in">
  <!-- Loading Spinner -->
  <div class="loading-overlay" *ngIf="isLoading">
    <div class="spinner-container">
      <app-loading-spinner></app-loading-spinner>
    </div>
  </div>

  <div [class.content-blur]="isLoading">
   
    <div class="modern-card">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
          <i class="fas fa-hourglass-half me-2"></i>
          Üyelik Bitişi Yaklaşanlar
        </h5>
        <div class="input-group search-box">
          <div class="input-group-prepend">
            <span class="input-group-text"><i class="fas fa-search"></i></span>
          </div>
          <input 
            type="text" 
            class="form-control" 
            [(ngModel)]="searchText"
            (ngModelChange)="filterMembers()"
          >
        </div>
      </div>
      <div class="card-body">
        <div class="table-responsive">
          <table class="modern-table">
            <thead>
              <tr>
                <th><PERSON><PERSON> Adı</th>
                <th>Telefon</th>
                <th>Branş</th>
                <th>
                  <div class="d-flex align-items-center">
                    Kalan Gün
                    <button class="sort-button ms-2" (click)="toggleSort()" title="Sıralama Yönünü Değiştir">
                      <i class="fas" [ngClass]="sortDirection === 'asc' ? 'fa-sort-amount-down' : 'fa-sort-amount-up'"></i>
                    </button>
                  </div>
                </th>
                <th class="text-center">İşlem</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let member of filteredMembers">
                <td>
                  <div class="member-info">
                    <div class="member-avatar" [style.backgroundColor]="getAvatarColor(member.memberName)">
                      {{ getInitials(member.memberName) }}
                    </div>
                    <div>{{ member.memberName }}</div>
                  </div>
                </td>
                <td>{{ member.phoneNumber }}</td>
                <td>{{ member.branch }}</td>
                <td>
                  <span [ngClass]="getRemainingDaysClass(member.remainingDays)">
                    {{ member.remainingDays }} Gün
                  </span>
                </td>
                <td class="text-center">
                  <button class="modern-btn modern-btn-outline-secondary" (click)="openWhatsApp(member.phoneNumber)">
                    <i class="fas fa-comment me-1"></i> Mesaj At
                  </button>
                </td>
              </tr>
              <tr *ngIf="filteredMembers.length === 0">
                <td colspan="5" class="text-center py-4">
                  <div class="empty-state">
                    <i class="fas fa-calendar-check fa-3x mb-3"></i>
                    <h5>Yaklaşan üyelik bitişi bulunamadı</h5>
                    <p class="text-muted">Şu anda üyelik bitişi yaklaşan üye bulunmamaktadır.</p>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
