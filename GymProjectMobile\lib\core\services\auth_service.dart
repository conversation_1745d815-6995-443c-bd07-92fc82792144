import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/auth_models.dart';
import '../models/user_model.dart';
import 'jwt_service.dart';
import 'logging_service.dart';

/// Authentication Service for GymKod Pro Mobile
class AuthService {
  static const String _tokenKey = 'auth_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _userKey = 'user_data';
  static const String _loginTimeKey = 'login_time';

  final JwtService _jwtService = JwtService();

  /// Kullanıcı giriş yapmış mı kontrol eder
  Future<bool> isLoggedIn() async {
    try {
      final token = await getToken();
      if (token == null) return false;

      // Token geçerli mi kontrol et
      if (_jwtService.isTokenExpired(token)) {
        await logout();
        return false;
      }

      return true;
    } catch (e) {
      LoggingService.error('Auth check failed: $e', tag: 'AUTH_SERVICE');
      return false;
    }
  }

  /// Token'ı getirir
  Future<String?> getToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_tokenKey);
    } catch (e) {
      LoggingService.error('Get token failed: $e', tag: 'AUTH_SERVICE');
      return null;
    }
  }

  /// Refresh token'ı getirir
  Future<String?> getRefreshToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_refreshTokenKey);
    } catch (e) {
      LoggingService.error('Get refresh token failed: $e', tag: 'AUTH_SERVICE');
      return null;
    }
  }

  /// Kullanıcı bilgilerini getirir
  Future<UserModel?> getCurrentUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString(_userKey);
      
      if (userJson == null) return null;
      
      final userMap = json.decode(userJson) as Map<String, dynamic>;
      return UserModel.fromJson(userMap);
    } catch (e) {
      LoggingService.error('Get current user failed: $e', tag: 'AUTH_SERVICE');
      return null;
    }
  }

  /// Kullanıcı ID'sini getirir
  Future<int?> getCurrentUserId() async {
    try {
      final user = await getCurrentUser();
      return user?.userID;
    } catch (e) {
      LoggingService.error('Get current user ID failed: $e', tag: 'AUTH_SERVICE');
      return null;
    }
  }

  /// Login işlemi sonrası token ve kullanıcı bilgilerini saklar
  Future<bool> saveAuthData(LoginResponseModel loginResponse) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Token'ları sakla
      await prefs.setString(_tokenKey, loginResponse.token);
      if (loginResponse.refreshToken != null) {
        await prefs.setString(_refreshTokenKey, loginResponse.refreshToken!);
      }
      
      // Kullanıcı bilgilerini sakla
      final userJson = json.encode(loginResponse.user.toJson());
      await prefs.setString(_userKey, userJson);
      
      // Login zamanını sakla
      await prefs.setInt(_loginTimeKey, DateTime.now().millisecondsSinceEpoch);
      
      LoggingService.info('Auth data saved successfully', tag: 'AUTH_SERVICE');
      return true;
    } catch (e) {
      LoggingService.error('Save auth data failed: $e', tag: 'AUTH_SERVICE');
      return false;
    }
  }

  /// Token'ı günceller (refresh token ile)
  Future<bool> updateToken(String newToken, {String? newRefreshToken}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      await prefs.setString(_tokenKey, newToken);
      if (newRefreshToken != null) {
        await prefs.setString(_refreshTokenKey, newRefreshToken);
      }
      
      LoggingService.info('Token updated successfully', tag: 'AUTH_SERVICE');
      return true;
    } catch (e) {
      LoggingService.error('Update token failed: $e', tag: 'AUTH_SERVICE');
      return false;
    }
  }

  /// Kullanıcı bilgilerini günceller
  Future<bool> updateUser(UserModel user) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = json.encode(user.toJson());
      await prefs.setString(_userKey, userJson);
      
      LoggingService.info('User data updated successfully', tag: 'AUTH_SERVICE');
      return true;
    } catch (e) {
      LoggingService.error('Update user failed: $e', tag: 'AUTH_SERVICE');
      return false;
    }
  }

  /// Logout işlemi - tüm auth verilerini temizler
  Future<bool> logout() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      await Future.wait([
        prefs.remove(_tokenKey),
        prefs.remove(_refreshTokenKey),
        prefs.remove(_userKey),
        prefs.remove(_loginTimeKey),
      ]);
      
      LoggingService.info('Logout successful', tag: 'AUTH_SERVICE');
      return true;
    } catch (e) {
      LoggingService.error('Logout failed: $e', tag: 'AUTH_SERVICE');
      return false;
    }
  }

  /// Login zamanını getirir
  Future<DateTime?> getLoginTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestamp = prefs.getInt(_loginTimeKey);
      
      if (timestamp == null) return null;
      
      return DateTime.fromMillisecondsSinceEpoch(timestamp);
    } catch (e) {
      LoggingService.error('Get login time failed: $e', tag: 'AUTH_SERVICE');
      return null;
    }
  }

  /// Token'ın süresi dolmuş mu kontrol eder
  Future<bool> isTokenExpired() async {
    try {
      final token = await getToken();
      if (token == null) return true;
      
      return _jwtService.isTokenExpired(token);
    } catch (e) {
      LoggingService.error('Token expiry check failed: $e', tag: 'AUTH_SERVICE');
      return true;
    }
  }

  /// Authorization header'ı getirir
  Future<Map<String, String>?> getAuthHeaders() async {
    try {
      final token = await getToken();
      if (token == null) return null;
      
      return {
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      };
    } catch (e) {
      LoggingService.error('Get auth headers failed: $e', tag: 'AUTH_SERVICE');
      return null;
    }
  }

  /// Kullanıcının rolünü getirir
  Future<String?> getUserRole() async {
    try {
      final user = await getCurrentUser();
      return user?.role;
    } catch (e) {
      LoggingService.error('Get user role failed: $e', tag: 'AUTH_SERVICE');
      return null;
    }
  }

  /// Kullanıcının company ID'sini getirir
  Future<int?> getCompanyId() async {
    try {
      final user = await getCurrentUser();
      return user?.companyID;
    } catch (e) {
      LoggingService.error('Get company ID failed: $e', tag: 'AUTH_SERVICE');
      return null;
    }
  }
}
