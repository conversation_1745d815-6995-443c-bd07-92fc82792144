using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Aspects.Autofac.Validation;
using Core.Utilities.Business;
using Core.Utilities.Results;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Business.Concrete
{
    /// <summary>
    /// Üye egzersiz ilerleme takip service implementasyonu
    /// Performance optimized for 10.000+ users
    /// </summary>
    public class MemberExerciseProgressManager : IMemberExerciseProgressService
    {
        private readonly IMemberExerciseProgressDal _memberExerciseProgressDal;
        private readonly IMemberDal _memberDal;
        private readonly IMemberWorkoutProgramDal _memberWorkoutProgramDal;
        private readonly IWorkoutProgramExerciseDal _workoutProgramExerciseDal;
        private readonly ICompanyContext _companyContext;

        public MemberExerciseProgressManager(
            IMemberExerciseProgressDal memberExerciseProgressDal,
            IMemberDal memberDal,
            IMemberWorkoutProgramDal memberWorkoutProgramDal,
            IWorkoutProgramExerciseDal workoutProgramExerciseDal,
            ICompanyContext companyContext)
        {
            _memberExerciseProgressDal = memberExerciseProgressDal;
            _memberDal = memberDal;
            _memberWorkoutProgramDal = memberWorkoutProgramDal;
            _workoutProgramExerciseDal = workoutProgramExerciseDal;
            _companyContext = companyContext;
        }

        [SecuredOperation("member,admin,owner")]
        [ValidationAspect(typeof(MemberExerciseProgressAddValidator))]
        [LogAspect]
        [TransactionScopeAspect]
        [SmartCacheRemoveAspect("MemberExerciseProgress")]
        [PerformanceAspect(2)]
        public IResult Add(MemberExerciseProgressAddDto progressAddDto)
        {
            // İş kuralları kontrolü
            IResult ruleResult = BusinessRules.Run(
                CheckIfMemberWorkoutProgramExists(progressAddDto.MemberWorkoutProgramID),
                CheckIfWorkoutProgramExerciseExists(progressAddDto.WorkoutProgramExerciseID),
                CheckIfMemberBelongsToProgram(progressAddDto.MemberWorkoutProgramID),
                CheckRateLimit(progressAddDto.MemberWorkoutProgramID),
                CheckCompletedSetsValid(progressAddDto.CompletedSets)
            );

            if (ruleResult != null)
            {
                return ruleResult;
            }

            var companyId = _companyContext.GetCompanyId();
            var memberId = GetMemberIdFromProgram(progressAddDto.MemberWorkoutProgramID);

            var progress = new MemberExerciseProgress
            {
                MemberWorkoutProgramID = progressAddDto.MemberWorkoutProgramID,
                WorkoutProgramExerciseID = progressAddDto.WorkoutProgramExerciseID,
                MemberID = memberId,
                CompanyID = companyId,
                CompletedDate = DateTime.Now,
                CompletedSets = progressAddDto.CompletedSets,
                ActualReps = progressAddDto.ActualReps,
                Notes = progressAddDto.Notes,
                CreationDate = DateTime.Now
            };

            _memberExerciseProgressDal.Add(progress);
            return new SuccessResult("Egzersiz başarıyla tamamlandı olarak işaretlendi.");
        }

        [SecuredOperation("member,admin,owner")]
        [ValidationAspect(typeof(MemberExerciseProgressUpdateValidator))]
        [LogAspect]
        [TransactionScopeAspect]
        [SmartCacheRemoveAspect("MemberExerciseProgress")]
        [PerformanceAspect(2)]
        public IResult Update(MemberExerciseProgressUpdateDto progressUpdateDto)
        {
            // İş kuralları kontrolü
            IResult ruleResult = BusinessRules.Run(
                CheckIfProgressExists(progressUpdateDto.MemberExerciseProgressID),
                CheckIfProgressBelongsToUser(progressUpdateDto.MemberExerciseProgressID),
                CheckCompletedSetsValid(progressUpdateDto.CompletedSets)
            );

            if (ruleResult != null)
            {
                return ruleResult;
            }

            var progress = _memberExerciseProgressDal.Get(p => p.MemberExerciseProgressID == progressUpdateDto.MemberExerciseProgressID);
            
            progress.CompletedSets = progressUpdateDto.CompletedSets;
            progress.ActualReps = progressUpdateDto.ActualReps;
            progress.Notes = progressUpdateDto.Notes;
            progress.UpdatedDate = DateTime.Now;

            _memberExerciseProgressDal.Update(progress);
            return new SuccessResult("Egzersiz ilerleme kaydı başarıyla güncellendi.");
        }

        [SecuredOperation("member,admin,owner")]
        [LogAspect]
        [TransactionScopeAspect]
        [SmartCacheRemoveAspect("MemberExerciseProgress")]
        [PerformanceAspect(2)]
        public IResult Delete(int memberExerciseProgressId)
        {
            // İş kuralları kontrolü
            IResult ruleResult = BusinessRules.Run(
                CheckIfProgressExists(memberExerciseProgressId),
                CheckIfProgressBelongsToUser(memberExerciseProgressId)
            );

            if (ruleResult != null)
            {
                return ruleResult;
            }

            var progress = _memberExerciseProgressDal.Get(p => p.MemberExerciseProgressID == memberExerciseProgressId);
            progress.DeletedDate = DateTime.Now;

            _memberExerciseProgressDal.Update(progress);
            return new SuccessResult("Egzersiz ilerleme kaydı başarıyla silindi.");
        }

        [SecuredOperation("member,admin,owner")]
        [MultiTenantCacheAspect(duration: 5, "MemberExerciseProgress", "ById")]
        [PerformanceAspect(1)]
        public IDataResult<MemberExerciseProgress> GetById(int memberExerciseProgressId)
        {
            var progress = _memberExerciseProgressDal.Get(p => p.MemberExerciseProgressID == memberExerciseProgressId && p.DeletedDate == null);
            
            if (progress == null)
            {
                return new ErrorDataResult<MemberExerciseProgress>("Egzersiz ilerleme kaydı bulunamadı.");
            }

            return new SuccessDataResult<MemberExerciseProgress>(progress);
        }

        [SecuredOperation("admin,owner")]
        [MultiTenantCacheAspect(duration: 10, "MemberExerciseProgress", "CompanyList")]
        [PerformanceAspect(3)]
        public IDataResult<List<MemberExerciseProgressListDto>> GetCompanyProgressList(int page = 1, int pageSize = 50)
        {
            var companyId = _companyContext.GetCompanyId();
            var progressList = _memberExerciseProgressDal.GetCompanyProgressList(companyId, page, pageSize);
            
            return new SuccessDataResult<List<MemberExerciseProgressListDto>>(progressList);
        }

        [SecuredOperation("member,admin,owner")]
        [MultiTenantCacheAspect(duration: 5, "MemberExerciseProgress", "MemberList")]
        [PerformanceAspect(2)]
        public IDataResult<List<MemberExerciseProgressDto>> GetMemberProgressList(int memberId, int page = 1, int pageSize = 50)
        {
            // İş kuralları kontrolü
            IResult ruleResult = BusinessRules.Run(
                CheckIfMemberExists(memberId),
                CheckIfMemberBelongsToCompany(memberId)
            );

            if (ruleResult != null)
            {
                return new ErrorDataResult<List<MemberExerciseProgressDto>>(ruleResult.Message);
            }

            var progressList = _memberExerciseProgressDal.GetMemberProgressList(memberId, page, pageSize);
            
            return new SuccessDataResult<List<MemberExerciseProgressDto>>(progressList);
        }

        [SecuredOperation("member,admin,owner")]
        [MultiTenantCacheAspect(duration: 5, "MemberExerciseProgress", "ByDate")]
        [PerformanceAspect(2)]
        public IDataResult<List<MemberExerciseProgressDto>> GetMemberProgressByDate(int memberId, DateTime date)
        {
            // İş kuralları kontrolü
            IResult ruleResult = BusinessRules.Run(
                CheckIfMemberExists(memberId),
                CheckIfMemberBelongsToCompany(memberId)
            );

            if (ruleResult != null)
            {
                return new ErrorDataResult<List<MemberExerciseProgressDto>>(ruleResult.Message);
            }

            var progressList = _memberExerciseProgressDal.GetMemberProgressByDate(memberId, date);

            return new SuccessDataResult<List<MemberExerciseProgressDto>>(progressList);
        }

        [SecuredOperation("member,admin,owner")]
        [MultiTenantCacheAspect(duration: 5, "MemberExerciseProgress", "ByProgram")]
        [PerformanceAspect(2)]
        public IDataResult<List<MemberExerciseProgressDto>> GetMemberProgressByProgram(int memberId, int memberWorkoutProgramId)
        {
            // İş kuralları kontrolü
            IResult ruleResult = BusinessRules.Run(
                CheckIfMemberExists(memberId),
                CheckIfMemberBelongsToCompany(memberId),
                CheckIfMemberWorkoutProgramExists(memberWorkoutProgramId)
            );

            if (ruleResult != null)
            {
                return new ErrorDataResult<List<MemberExerciseProgressDto>>(ruleResult.Message);
            }

            var progressList = _memberExerciseProgressDal.GetMemberProgressByProgram(memberId, memberWorkoutProgramId);

            return new SuccessDataResult<List<MemberExerciseProgressDto>>(progressList);
        }

        [SecuredOperation("member,admin,owner")]
        [MultiTenantCacheAspect(duration: 10, "MemberExerciseProgress", "Stats")]
        [PerformanceAspect(2)]
        public IDataResult<MemberExerciseProgressStatsDto> GetMemberProgressStats(int memberId)
        {
            // İş kuralları kontrolü
            IResult ruleResult = BusinessRules.Run(
                CheckIfMemberExists(memberId),
                CheckIfMemberBelongsToCompany(memberId)
            );

            if (ruleResult != null)
            {
                return new ErrorDataResult<MemberExerciseProgressStatsDto>(ruleResult.Message);
            }

            var stats = _memberExerciseProgressDal.GetMemberProgressStats(memberId);

            return new SuccessDataResult<MemberExerciseProgressStatsDto>(stats);
        }

        [SecuredOperation("member,admin,owner")]
        [MultiTenantCacheAspect(duration: 2, "MemberExerciseProgress", "CompletionStatus")]
        [PerformanceAspect(1)]
        public IDataResult<ExerciseCompletionStatusDto> GetExerciseCompletionStatus(int memberId, int workoutProgramExerciseId, DateTime date)
        {
            // İş kuralları kontrolü
            IResult ruleResult = BusinessRules.Run(
                CheckIfMemberExists(memberId),
                CheckIfMemberBelongsToCompany(memberId),
                CheckIfWorkoutProgramExerciseExists(workoutProgramExerciseId)
            );

            if (ruleResult != null)
            {
                return new ErrorDataResult<ExerciseCompletionStatusDto>(ruleResult.Message);
            }

            var status = _memberExerciseProgressDal.GetExerciseCompletionStatus(memberId, workoutProgramExerciseId, date);

            return new SuccessDataResult<ExerciseCompletionStatusDto>(status);
        }

        [SecuredOperation("member,admin,owner")]
        [MultiTenantCacheAspect(duration: 5, "MemberExerciseProgress", "DailyProgress")]
        [PerformanceAspect(2)]
        public IDataResult<DailyWorkoutProgressDto> GetDailyWorkoutProgress(int memberId, DateTime date)
        {
            // İş kuralları kontrolü
            IResult ruleResult = BusinessRules.Run(
                CheckIfMemberExists(memberId),
                CheckIfMemberBelongsToCompany(memberId)
            );

            if (ruleResult != null)
            {
                return new ErrorDataResult<DailyWorkoutProgressDto>(ruleResult.Message);
            }

            var progress = _memberExerciseProgressDal.GetDailyWorkoutProgress(memberId, date);

            return new SuccessDataResult<DailyWorkoutProgressDto>(progress);
        }

        [SecuredOperation("member,admin,owner")]
        [MultiTenantCacheAspect(duration: 10, "MemberExerciseProgress", "WeeklyProgress")]
        [PerformanceAspect(3)]
        public IDataResult<List<DailyWorkoutProgressDto>> GetWeeklyWorkoutProgress(int memberId, DateTime startDate)
        {
            // İş kuralları kontrolü
            IResult ruleResult = BusinessRules.Run(
                CheckIfMemberExists(memberId),
                CheckIfMemberBelongsToCompany(memberId)
            );

            if (ruleResult != null)
            {
                return new ErrorDataResult<List<DailyWorkoutProgressDto>>(ruleResult.Message);
            }

            var progress = _memberExerciseProgressDal.GetWeeklyWorkoutProgress(memberId, startDate);

            return new SuccessDataResult<List<DailyWorkoutProgressDto>>(progress);
        }

        [SecuredOperation("member,admin,owner")]
        [MultiTenantCacheAspect(duration: 15, "MemberExerciseProgress", "MonthlyProgress")]
        [PerformanceAspect(3)]
        public IDataResult<List<DailyWorkoutProgressDto>> GetMonthlyWorkoutProgress(int memberId, int year, int month)
        {
            // İş kuralları kontrolü
            IResult ruleResult = BusinessRules.Run(
                CheckIfMemberExists(memberId),
                CheckIfMemberBelongsToCompany(memberId)
            );

            if (ruleResult != null)
            {
                return new ErrorDataResult<List<DailyWorkoutProgressDto>>(ruleResult.Message);
            }

            var progress = _memberExerciseProgressDal.GetMonthlyWorkoutProgress(memberId, year, month);

            return new SuccessDataResult<List<DailyWorkoutProgressDto>>(progress);
        }

        [SecuredOperation("member,admin,owner")]
        [MultiTenantCacheAspect(duration: 5, "MemberExerciseProgress", "ProgramCompletion")]
        [PerformanceAspect(2)]
        public IDataResult<List<ExerciseCompletionStatusDto>> GetProgramExerciseCompletionStatus(int memberId, int memberWorkoutProgramId, DateTime date)
        {
            // İş kuralları kontrolü
            IResult ruleResult = BusinessRules.Run(
                CheckIfMemberExists(memberId),
                CheckIfMemberBelongsToCompany(memberId),
                CheckIfMemberWorkoutProgramExists(memberWorkoutProgramId)
            );

            if (ruleResult != null)
            {
                return new ErrorDataResult<List<ExerciseCompletionStatusDto>>(ruleResult.Message);
            }

            var statusList = _memberExerciseProgressDal.GetProgramExerciseCompletionStatus(memberId, memberWorkoutProgramId, date);

            return new SuccessDataResult<List<ExerciseCompletionStatusDto>>(statusList);
        }

        [SecuredOperation("admin,owner")]
        [MultiTenantCacheAspect(duration: 15, "MemberExerciseProgress", "CompanyStats")]
        [PerformanceAspect(3)]
        public IDataResult<List<MemberExerciseProgressStatsDto>> GetCompanyProgressStats(DateTime startDate, DateTime endDate)
        {
            var companyId = _companyContext.GetCompanyId();
            var stats = _memberExerciseProgressDal.GetCompanyProgressStats(companyId, startDate, endDate);

            return new SuccessDataResult<List<MemberExerciseProgressStatsDto>>(stats);
        }

        [SecuredOperation("admin,owner")]
        [MultiTenantCacheAspect(duration: 10, "MemberExerciseProgress", "TopActive")]
        [PerformanceAspect(2)]
        public IDataResult<List<MemberExerciseProgressStatsDto>> GetTopActiveMembers(int topCount = 10, DateTime? startDate = null)
        {
            var companyId = _companyContext.GetCompanyId();
            var topMembers = _memberExerciseProgressDal.GetTopActiveMembers(companyId, topCount, startDate);

            return new SuccessDataResult<List<MemberExerciseProgressStatsDto>>(topMembers);
        }

        [SecuredOperation("admin,owner")]
        [MultiTenantCacheAspect(duration: 15, "MemberExerciseProgress", "TotalCount")]
        [PerformanceAspect(2)]
        public IDataResult<int> GetTotalCompletedExercisesCount(DateTime startDate, DateTime endDate)
        {
            var companyId = _companyContext.GetCompanyId();
            var count = _memberExerciseProgressDal.GetTotalCompletedExercisesCount(companyId, startDate, endDate);

            return new SuccessDataResult<int>(count);
        }

        // Mobil API metodları
        [SecuredOperation("member")]
        [MultiTenantCacheAspect(duration: 2, "MemberExerciseProgress", "UserCompletion")]
        [PerformanceAspect(2)]
        public IDataResult<List<ExerciseCompletionStatusDto>> GetExerciseCompletionStatusByUserId(int userId, DateTime date)
        {
            var member = GetMemberByUserId(userId);
            if (member == null)
            {
                return new ErrorDataResult<List<ExerciseCompletionStatusDto>>("Üye bulunamadı.");
            }

            // Üyenin aktif programlarını al
            var activePrograms = _memberWorkoutProgramDal.GetMemberActivePrograms(member.MemberID);
            if (!activePrograms.Any())
            {
                return new SuccessDataResult<List<ExerciseCompletionStatusDto>>(new List<ExerciseCompletionStatusDto>(), "Aktif program bulunamadı.");
            }

            var allStatuses = new List<ExerciseCompletionStatusDto>();
            foreach (var program in activePrograms)
            {
                var statuses = _memberExerciseProgressDal.GetProgramExerciseCompletionStatus(member.MemberID, program.MemberWorkoutProgramID, date);
                allStatuses.AddRange(statuses);
            }

            return new SuccessDataResult<List<ExerciseCompletionStatusDto>>(allStatuses);
        }

        [SecuredOperation("member")]
        [MultiTenantCacheAspect(duration: 5, "MemberExerciseProgress", "UserDailyProgress")]
        [PerformanceAspect(2)]
        public IDataResult<DailyWorkoutProgressDto> GetDailyWorkoutProgressByUserId(int userId, DateTime date)
        {
            var member = GetMemberByUserId(userId);
            if (member == null)
            {
                return new ErrorDataResult<DailyWorkoutProgressDto>("Üye bulunamadı.");
            }

            var progress = _memberExerciseProgressDal.GetDailyWorkoutProgress(member.MemberID, date);

            return new SuccessDataResult<DailyWorkoutProgressDto>(progress);
        }

        [SecuredOperation("member")]
        [ValidationAspect(typeof(MemberExerciseProgressAddValidator))]
        [LogAspect]
        [TransactionScopeAspect]
        [SmartCacheRemoveAspect("MemberExerciseProgress")]
        [PerformanceAspect(2)]
        public IResult AddProgressByUserId(int userId, MemberExerciseProgressAddDto progressAddDto)
        {
            var member = GetMemberByUserId(userId);
            if (member == null)
            {
                return new ErrorResult("Üye bulunamadı.");
            }

            // Rate limiting kontrolü
            var lastProgressTime = _memberExerciseProgressDal.GetLastProgressTime(member.MemberID);
            if (lastProgressTime.HasValue && DateTime.Now.Subtract(lastProgressTime.Value).TotalSeconds < 1)
            {
                return new ErrorResult("Çok hızlı işlem yapıyorsunuz. Lütfen 1 saniye bekleyin.");
            }

            // Aynı gün aynı egzersizin tamamlanıp tamamlanmadığını kontrol et
            if (_memberExerciseProgressDal.IsExerciseCompletedToday(member.MemberID, progressAddDto.WorkoutProgramExerciseID, DateTime.Now))
            {
                return new ErrorResult("Bu egzersiz bugün zaten tamamlanmış.");
            }

            var progress = new MemberExerciseProgress
            {
                MemberWorkoutProgramID = progressAddDto.MemberWorkoutProgramID,
                WorkoutProgramExerciseID = progressAddDto.WorkoutProgramExerciseID,
                MemberID = member.MemberID,
                CompanyID = member.CompanyID,
                CompletedDate = DateTime.Now,
                CompletedSets = progressAddDto.CompletedSets,
                ActualReps = progressAddDto.ActualReps,
                Notes = progressAddDto.Notes,
                CreationDate = DateTime.Now
            };

            _memberExerciseProgressDal.Add(progress);
            return new SuccessResult("Egzersiz başarıyla tamamlandı olarak işaretlendi.");
        }

        // Business Rules
        private IResult CheckIfMemberWorkoutProgramExists(int memberWorkoutProgramId)
        {
            var program = _memberWorkoutProgramDal.Get(mwp => mwp.MemberWorkoutProgramID == memberWorkoutProgramId);
            if (program == null)
            {
                return new ErrorResult("Program ataması bulunamadı.");
            }
            return new SuccessResult();
        }

        private IResult CheckIfWorkoutProgramExerciseExists(int workoutProgramExerciseId)
        {
            var exercise = _workoutProgramExerciseDal.Get(wpe => wpe.WorkoutProgramExerciseID == workoutProgramExerciseId);
            if (exercise == null)
            {
                return new ErrorResult("Egzersiz bulunamadı.");
            }
            return new SuccessResult();
        }

        private IResult CheckIfMemberBelongsToProgram(int memberWorkoutProgramId)
        {
            var companyId = _companyContext.GetCompanyId();
            var program = _memberWorkoutProgramDal.Get(mwp => mwp.MemberWorkoutProgramID == memberWorkoutProgramId && mwp.CompanyID == companyId);
            if (program == null)
            {
                return new ErrorResult("Bu programa erişim yetkiniz bulunmuyor.");
            }
            return new SuccessResult();
        }

        private IResult CheckRateLimit(int memberWorkoutProgramId)
        {
            var program = _memberWorkoutProgramDal.Get(mwp => mwp.MemberWorkoutProgramID == memberWorkoutProgramId);
            if (program == null) return new SuccessResult();

            var lastProgressTime = _memberExerciseProgressDal.GetLastProgressTime(program.MemberID);
            if (lastProgressTime.HasValue && DateTime.Now.Subtract(lastProgressTime.Value).TotalSeconds < 1)
            {
                return new ErrorResult("Çok hızlı işlem yapıyorsunuz. Lütfen 1 saniye bekleyin.");
            }
            return new SuccessResult();
        }

        private IResult CheckCompletedSetsValid(int completedSets)
        {
            if (completedSets < 0 || completedSets > 50)
            {
                return new ErrorResult("Tamamlanan set sayısı 0-50 arasında olmalıdır.");
            }
            return new SuccessResult();
        }

        private IResult CheckIfProgressExists(int memberExerciseProgressId)
        {
            var progress = _memberExerciseProgressDal.Get(p => p.MemberExerciseProgressID == memberExerciseProgressId && p.DeletedDate == null);
            if (progress == null)
            {
                return new ErrorResult("Egzersiz ilerleme kaydı bulunamadı.");
            }
            return new SuccessResult();
        }

        private IResult CheckIfProgressBelongsToUser(int memberExerciseProgressId)
        {
            var companyId = _companyContext.GetCompanyId();
            var progress = _memberExerciseProgressDal.Get(p => p.MemberExerciseProgressID == memberExerciseProgressId && p.CompanyID == companyId);
            if (progress == null)
            {
                return new ErrorResult("Bu ilerleme kaydına erişim yetkiniz bulunmuyor.");
            }
            return new SuccessResult();
        }

        private IResult CheckIfMemberExists(int memberId)
        {
            var member = _memberDal.Get(m => m.MemberID == memberId);
            if (member == null)
            {
                return new ErrorResult("Üye bulunamadı.");
            }
            return new SuccessResult();
        }

        private IResult CheckIfMemberBelongsToCompany(int memberId)
        {
            var companyId = _companyContext.GetCompanyId();
            var member = _memberDal.Get(m => m.MemberID == memberId && m.CompanyID == companyId);
            if (member == null)
            {
                return new ErrorResult("Bu üyeye erişim yetkiniz bulunmuyor.");
            }
            return new SuccessResult();
        }

        // Helper metodlar
        private int GetMemberIdFromProgram(int memberWorkoutProgramId)
        {
            var program = _memberWorkoutProgramDal.Get(mwp => mwp.MemberWorkoutProgramID == memberWorkoutProgramId);
            return program?.MemberID ?? 0;
        }

        private Member GetMemberByUserId(int userId)
        {
            var companyId = _companyContext.GetCompanyId();
            return _memberDal.Get(m => m.UserID == userId && m.CompanyID == companyId);
        }
    }
}
