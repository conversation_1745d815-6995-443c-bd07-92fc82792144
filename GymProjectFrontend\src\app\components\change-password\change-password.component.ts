import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { AuthService } from '../../services/auth.service';
import { finalize } from 'rxjs';

@Component({
  selector: 'app-change-password',
  templateUrl: './change-password.component.html',
  styleUrl: './change-password.component.css',
  standalone: false,
})
export class ChangePasswordComponent implements OnInit {
  changePasswordForm: FormGroup;
  isLoading: boolean = false;
  currentPasswordVisible: boolean = false;
  newPasswordVisible: boolean = false;
  confirmPasswordVisible: boolean = false;
  isFirstTimeLogin: boolean = false;

  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private toastrService: ToastrService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.createChangePasswordForm();
    this.checkIfFirstTimeLogin();
  }

  createChangePasswordForm() {
    this.changePasswordForm = this.formBuilder.group({
      currentPassword: ['', Validators.required],
      newPassword: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', Validators.required]
    }, {
      validator: this.passwordMatchValidator
    });
  }

  // Şifre eşleşme kontrolü
  passwordMatchValidator(g: FormGroup) {
    const newPassword = g.get('newPassword')?.value || '';
    const confirmPassword = g.get('confirmPassword')?.value || '';
    return newPassword === confirmPassword ? null : { 'mismatch': true };
  }

  // İlk giriş kontrolü
  checkIfFirstTimeLogin() {
    const requirePasswordChange = localStorage.getItem('requirePasswordChange');
    this.isFirstTimeLogin = requirePasswordChange === 'true';
  }

  changePassword() {
    if (this.changePasswordForm.invalid) {
      this.toastrService.error('Lütfen tüm alanları doğru şekilde doldurun');
      return;
    }

    if (this.changePasswordForm.hasError('mismatch')) {
      this.toastrService.error('Yeni şifre ve şifre tekrarı eşleşmiyor');
      return;
    }

    const currentPassword = this.changePasswordForm.get('currentPassword')?.value || '';
    const newPassword = this.changePasswordForm.get('newPassword')?.value || '';

    this.isLoading = true;
    this.authService.changePassword(currentPassword, newPassword)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.toastrService.success(response.message || 'Şifreniz başarıyla değiştirildi');
            localStorage.removeItem('requirePasswordChange');
            this.router.navigate(['/todayentries']);
          } else {
            this.toastrService.error(response.message || 'Şifre değiştirme işlemi başarısız');
          }
        },
        error: (error) => {
          this.toastrService.error(error.message || 'Şifre değiştirme işlemi sırasında bir hata oluştu');
        }
      });
  }

  // Şifre görünürlüğünü değiştirir
  toggleCurrentPasswordVisibility(): void {
    this.currentPasswordVisible = !this.currentPasswordVisible;
  }

  toggleNewPasswordVisibility(): void {
    this.newPasswordVisible = !this.newPasswordVisible;
  }

  toggleConfirmPasswordVisibility(): void {
    this.confirmPasswordVisible = !this.confirmPasswordVisible;
  }
}