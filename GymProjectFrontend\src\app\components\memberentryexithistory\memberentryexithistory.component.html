<div class="container mt-4">
  <div *ngIf="isLoading" class="d-flex justify-content-center align-items-center" style="height: 100vh;">
    <app-loading-spinner></app-loading-spinner>
  </div>

  <div class="row" [class.content-blur]="isLoading">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <h5 class="card-title">Salona Girenler ve Süreleri</h5>
          <table class="table table-striped table-bordered">
            <thead class="thead-dark">
              <tr>
                <th>Salona Girenler</th>
                <th>Süre (dk)</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let memberEntryExitHistory of memberEntryExitHistories.reverse(); let i = index">
                <td>{{ memberEntryExitHistory.memberName }}</td>
                <td>{{ memberEntryExitHistory.timeDifferenceInMinutes }} dk</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>