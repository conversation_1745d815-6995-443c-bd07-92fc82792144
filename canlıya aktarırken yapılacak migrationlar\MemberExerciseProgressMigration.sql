-- <PERSON>ye Egzersiz İlerleme Takip Sistemi Migration Script
-- Bu script egzersiz ilerleme takip tablosunu ve ilgili yapıları oluşturur
-- Performance optimized for 10.000+ users

USE [GymProject]
GO

-- MemberExerciseProgress tablosunu oluştur
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[MemberExerciseProgress]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[MemberExerciseProgress](
        [MemberExerciseProgressID] [int] IDENTITY(1,1) NOT NULL,
        [MemberWorkoutProgramID] [int] NOT NULL,
        [WorkoutProgramExerciseID] [int] NOT NULL,
        [CompletedDate] [datetime2](7) NOT NULL,
        [CompletedSets] [int] NOT NULL,
        [ActualReps] [nvarchar](50) NULL,
        [Notes] [nvarchar](500) NULL,
        [CompanyID] [int] NOT NULL,
        [CreationDate] [datetime2](7) NOT NULL,
        [UpdatedDate] [datetime2](7) NULL,
        [DeletedDate] [datetime2](7) NULL,
        [IsActive] [bit] NOT NULL,
        CONSTRAINT [PK_MemberExerciseProgress] PRIMARY KEY CLUSTERED 
        (
            [MemberExerciseProgressID] ASC
        )WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
    ) ON [PRIMARY]

    PRINT 'MemberExerciseProgress tablosu oluşturuldu.'
END
ELSE
BEGIN
    PRINT 'MemberExerciseProgress tablosu zaten mevcut.'
END
GO

-- Foreign Key Constraints
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE object_id = OBJECT_ID(N'[dbo].[FK_MemberExerciseProgress_MemberWorkoutPrograms]') AND parent_object_id = OBJECT_ID(N'[dbo].[MemberExerciseProgress]'))
BEGIN
    ALTER TABLE [dbo].[MemberExerciseProgress] WITH CHECK ADD CONSTRAINT [FK_MemberExerciseProgress_MemberWorkoutPrograms] 
    FOREIGN KEY([MemberWorkoutProgramID]) REFERENCES [dbo].[MemberWorkoutPrograms] ([MemberWorkoutProgramID])
    
    ALTER TABLE [dbo].[MemberExerciseProgress] CHECK CONSTRAINT [FK_MemberExerciseProgress_MemberWorkoutPrograms]
    PRINT 'MemberWorkoutPrograms foreign key eklendi.'
END
GO

IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE object_id = OBJECT_ID(N'[dbo].[FK_MemberExerciseProgress_WorkoutProgramExercises]') AND parent_object_id = OBJECT_ID(N'[dbo].[MemberExerciseProgress]'))
BEGIN
    ALTER TABLE [dbo].[MemberExerciseProgress] WITH CHECK ADD CONSTRAINT [FK_MemberExerciseProgress_WorkoutProgramExercises] 
    FOREIGN KEY([WorkoutProgramExerciseID]) REFERENCES [dbo].[WorkoutProgramExercises] ([WorkoutProgramExerciseID])
    
    ALTER TABLE [dbo].[MemberExerciseProgress] CHECK CONSTRAINT [FK_MemberExerciseProgress_WorkoutProgramExercises]
    PRINT 'WorkoutProgramExercises foreign key eklendi.'
END
GO

-- PERFORMANCE INDEXES (10.000+ kullanıcı için optimize edilmiş)

-- 1. Composite index: CompanyID + MemberWorkoutProgramID + CompletedDate (En sık kullanılan sorgu)
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[MemberExerciseProgress]') AND name = N'IX_MemberExerciseProgress_Company_Program_Date')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_MemberExerciseProgress_Company_Program_Date] 
    ON [dbo].[MemberExerciseProgress] ([CompanyID] ASC, [MemberWorkoutProgramID] ASC, [CompletedDate] ASC)
    INCLUDE ([WorkoutProgramExerciseID], [CompletedSets], [ActualReps], [IsActive])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    
    PRINT 'Performance index (Company-Program-Date) eklendi.'
END
GO

-- 2. Composite index: WorkoutProgramExerciseID + CompletedDate (Egzersiz bazlı sorgular için)
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[MemberExerciseProgress]') AND name = N'IX_MemberExerciseProgress_Exercise_Date')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_MemberExerciseProgress_Exercise_Date] 
    ON [dbo].[MemberExerciseProgress] ([WorkoutProgramExerciseID] ASC, [CompletedDate] ASC)
    INCLUDE ([MemberWorkoutProgramID], [CompletedSets], [IsActive])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    
    PRINT 'Performance index (Exercise-Date) eklendi.'
END
GO

-- 3. Filtered index: Sadece aktif kayıtlar için (IsActive = 1)
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[MemberExerciseProgress]') AND name = N'IX_MemberExerciseProgress_Active_CompletedDate')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_MemberExerciseProgress_Active_CompletedDate] 
    ON [dbo].[MemberExerciseProgress] ([CompletedDate] ASC)
    INCLUDE ([MemberWorkoutProgramID], [WorkoutProgramExerciseID], [CompletedSets])
    WHERE ([IsActive] = 1)
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    
    PRINT 'Filtered index (Active records) eklendi.'
END
GO

-- 4. Covering index: Analytics sorguları için
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[MemberExerciseProgress]') AND name = N'IX_MemberExerciseProgress_Analytics')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_MemberExerciseProgress_Analytics] 
    ON [dbo].[MemberExerciseProgress] ([CompanyID] ASC, [CompletedDate] ASC)
    INCLUDE ([MemberWorkoutProgramID], [WorkoutProgramExerciseID], [CompletedSets], [IsActive])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
    
    PRINT 'Analytics index eklendi.'
END
GO

-- Default Constraints
IF NOT EXISTS (SELECT * FROM sys.default_constraints WHERE object_id = OBJECT_ID(N'[dbo].[DF_MemberExerciseProgress_CreationDate]') AND parent_object_id = OBJECT_ID(N'[dbo].[MemberExerciseProgress]'))
BEGIN
    ALTER TABLE [dbo].[MemberExerciseProgress] ADD CONSTRAINT [DF_MemberExerciseProgress_CreationDate] DEFAULT (getdate()) FOR [CreationDate]
    PRINT 'CreationDate default constraint eklendi.'
END
GO

IF NOT EXISTS (SELECT * FROM sys.default_constraints WHERE object_id = OBJECT_ID(N'[dbo].[DF_MemberExerciseProgress_IsActive]') AND parent_object_id = OBJECT_ID(N'[dbo].[MemberExerciseProgress]'))
BEGIN
    ALTER TABLE [dbo].[MemberExerciseProgress] ADD CONSTRAINT [DF_MemberExerciseProgress_IsActive] DEFAULT ((1)) FOR [IsActive]
    PRINT 'IsActive default constraint eklendi.'
END
GO

-- Check Constraints (Data integrity için)
IF NOT EXISTS (SELECT * FROM sys.check_constraints WHERE object_id = OBJECT_ID(N'[dbo].[CK_MemberExerciseProgress_CompletedSets]') AND parent_object_id = OBJECT_ID(N'[dbo].[MemberExerciseProgress]'))
BEGIN
    ALTER TABLE [dbo].[MemberExerciseProgress] WITH CHECK ADD CONSTRAINT [CK_MemberExerciseProgress_CompletedSets] 
    CHECK (([CompletedSets] >= 0 AND [CompletedSets] <= 50))
    
    ALTER TABLE [dbo].[MemberExerciseProgress] CHECK CONSTRAINT [CK_MemberExerciseProgress_CompletedSets]
    PRINT 'CompletedSets check constraint eklendi (0-50 arası).'
END
GO

-- PERFORMANCE STATISTICS UPDATE
-- İstatistikleri güncelle (Query optimizer için)
UPDATE STATISTICS [dbo].[MemberExerciseProgress]
GO

PRINT '=== MemberExerciseProgress Migration Tamamlandı ==='
PRINT 'Tablo: MemberExerciseProgress'
PRINT 'Foreign Keys: 2 adet'
PRINT 'Performance Indexes: 4 adet'
PRINT 'Constraints: 3 adet'
PRINT 'Sistem 10.000+ kullanıcı için optimize edildi.'
GO
