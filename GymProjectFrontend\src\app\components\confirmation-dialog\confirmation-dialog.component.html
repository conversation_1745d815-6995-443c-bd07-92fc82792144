<div class="modern-dialog fade-in">
  <div class="modern-dialog-header">
    <h2 class="modern-dialog-title">{{ data.title }}</h2>
    <button class="modern-btn modern-btn-sm" (click)="onNoClick()">
      <i class="fas fa-times"></i>
    </button>
  </div>

  <div class="modern-dialog-content">
    <div class="confirmation-icon" [ngClass]="getIconClass()">
      <i class="fas" [ngClass]="getIconType()"></i>
    </div>
    
    <div class="confirmation-message">
      {{ data.message }}
    </div>
  </div>

  <div class="modern-dialog-footer">
    <button class="modern-btn modern-btn-secondary" (click)="onNoClick()">
      <i class="fas fa-times modern-btn-icon"></i> {{ data.cancelText || 'İptal' }}
    </button>
    <button class="modern-btn" [ngClass]="getButtonClass()" (click)="onYesClick()">
      <i class="fas" [ngClass]="getButtonIcon()"></i> {{ data.confirmText || 'Onayla' }}
    </button>
  </div>
</div>

<style>
  .modern-dialog {
    max-width: 400px;
    width: auto;
    background-color: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
  }

  .modern-dialog-header {
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--bg-secondary);
  }

  .modern-dialog-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
  }

  .modern-dialog-content {
    padding: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .modern-dialog-footer {
    padding: var(--spacing-md) var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    background-color: var(--bg-secondary);
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-sm);
  }

  .confirmation-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-md);
  }

  .confirmation-icon i {
    font-size: 2rem;
    color: white;
  }

  .confirmation-icon.warning {
    background-color: var(--warning);
  }

  .confirmation-icon.danger {
    background-color: var(--danger);
  }

  .confirmation-icon.info {
    background-color: var(--info);
  }

  .confirmation-icon.success {
    background-color: var(--success);
  }

  .confirmation-message {
    font-size: 1.1rem;
    margin-bottom: var(--spacing-md);
    line-height: 1.5;
    white-space: pre-line;
  }
</style>
