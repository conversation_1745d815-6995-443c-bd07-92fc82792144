import { Component, Inject, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { ExpenseService } from '../../services/expense.service';
import { Expense } from '../../models/expense.model';
import { ExpenseDto } from '../../models/expenseDto.model';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-expense-dialog',
  templateUrl: './expense-dialog.component.html',
  styleUrls: ['./expense-dialog.component.css'],
  standalone: false
})
export class ExpenseDialogComponent implements OnInit {

  expenseForm: FormGroup;
  isEditMode: boolean = false;
  isLoading: boolean = false;

  expenseTypes: string[] = [
    'Fatura - Elektrik', '<PERSON>ura - Su', '<PERSON>ura - Doğalgaz', 'Fatura - İnternet',
    '<PERSON><PERSON>ş Ödemesi', '<PERSON>', '<PERSON>ze<PERSON> Alımı', 'Temizlik Malzemesi',
    '<PERSON>is Gideri', 'Bakım/Onarım', 'Vergi/Harç', 'Diğer'
  ];

  constructor(
    private fb: FormBuilder,
    public dialogRef: MatDialogRef<ExpenseDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ExpenseDto | null,
    private expenseService: ExpenseService,
    private toastrService: ToastrService
  ) {
    this.isEditMode = !!data;
    // FormGroup constructor içinde veya ngOnInit'de tanımlanmalı
    this.expenseForm = this.fb.group({}); // Boş başlat
  }

  ngOnInit(): void {
    this.createExpenseForm();
  }

  createExpenseForm(): void {
    this.expenseForm = this.fb.group({
      expenseID: [this.data?.expenseID || 0],
      description: [this.data?.description || '', Validators.maxLength(500)], // İsteğe bağlı
      amount: [this.data?.amount || null, [Validators.required, Validators.min(0.01)]],
      // expenseDate kontrolü (mat-datepicker için)
      expenseDate: [this.data?.expenseDate ? new Date(this.data.expenseDate) : null, Validators.required], // Yeni eklerken varsayılan null
      expenseType: [this.data?.expenseType || '', [Validators.required, Validators.maxLength(100)]]
    });
  }

  saveExpense(): void {
    if (this.expenseForm.invalid) {
      this.toastrService.warning('Lütfen formdaki tüm zorunlu alanları doğru şekilde doldurun.', 'Uyarı');
      this.expenseForm.markAllAsTouched();
      return;
    }

    this.isLoading = true;

    // Formdan gelen tarihi al (bu yerel saat diliminde olabilir)
    // Formdan gelen tarihi al (bu yerel saat diliminde olabilir)
    const localDate: Date | null = this.expenseForm.value.expenseDate;
    let utcDate: Date | null = null;

    if (localDate && !isNaN(localDate.getTime())) {
        const year = localDate.getFullYear();
        const month = localDate.getMonth(); // 0-11 arası
        const day = localDate.getDate();
        // UTC olarak ilgili günün gece yarısını temsil eden yeni bir Date nesnesi oluştur
        utcDate = new Date(Date.UTC(year, month, day, 0, 0, 0, 0));
    } else {
        this.toastrService.error('Geçersiz tarih seçimi.', 'Hata');
        this.isLoading = false;
        return;
    }

    const expenseData: Expense = {
      // Formdaki diğer değerleri alırken expenseDate'i hariç tut
      expenseID: this.expenseForm.value.expenseID,
      description: this.expenseForm.value.description,
      amount: this.expenseForm.value.amount,
      expenseType: this.expenseForm.value.expenseType,
      // Hesaplanan UTC tarihini ata
      expenseDate: utcDate,
      companyID: 0, // Backend'de atanacak
      isActive: true, // Backend'de yönetilebilir
      creationDate: this.data?.creationDate || new Date() // Interface'e uymak için eklendi
    };

     // Eğer description boşsa undefined ata
     if (!expenseData.description?.trim()) {
      expenseData.description = undefined;
    }

    const request$: Observable<any> = this.isEditMode
      ? this.expenseService.update(expenseData)
      : this.expenseService.add(expenseData);

    request$.subscribe({
      next: (response) => {
        if (response.success) {
          this.toastrService.success(this.isEditMode ? 'Gider başarıyla güncellendi.' : 'Gider başarıyla eklendi.', 'Başarılı');
          this.dialogRef.close(true);
        } else {
          this.toastrService.error(response.message || 'İşlem sırasında bir hata oluştu.', 'Hata');
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error saving expense:', error);
        const errorMessage = error.error?.message || error.error?.Message || error.message || 'İşlem sırasında bir sunucu hatası oluştu.';
        this.toastrService.error(errorMessage, 'Hata');
        this.isLoading = false;
      }
    });
  }

  closeDialog(): void {
    this.dialogRef.close();
  }

  // Form kontrollerine kolay erişim için getter'lar
  get description() { return this.expenseForm.get('description'); }
  get amount() { return this.expenseForm.get('amount'); }
  get expenseDate() { return this.expenseForm.get('expenseDate'); }
  get expenseType() { return this.expenseForm.get('expenseType'); }
}