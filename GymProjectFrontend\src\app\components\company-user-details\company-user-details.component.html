<div class="container mt-4">
  <div class="row">
    <div class="col-md-2">
      <div class="card">
        <div class="card-body">
          <label for="filterText" class="form-label"><strong><PERSON><PERSON><PERSON>, Şehir, Telefon Filtrelemesi</strong></label>
          <input
            type="text"
            class="form-control"
            [(ngModel)]="filterText"
            id="filterText"
          />
        </div>
      </div>
    </div>
    <div class="col-md-10">
      <div class="card">
        <div class="card-body">
          <h5 class="card-title">Kullanıcı Detayları</h5>
          <div class="table-responsive">
            <table class="table table-striped table-bordered">
              <thead class="thead-dark">
                <tr>
                  <th>Id</th>
                  <th>Adı</th>
                  <th>Telefon</th>
                  <th>E Posta</th>
                  <th>İl</th>
                  <th>İlçe</th>
                  <th>Şirket Adı</th>
                  <th>Şirket Telefon</th>
                  <th>Şirket Adres</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let companyUserDetail of companyUserDetails | filterPipe:filterText">
                  <td>{{ companyUserDetail.companyUserId }}</td>
                  <td>{{ companyUserDetail.companyUserName }}</td>
                  <td>{{ companyUserDetail.companyUserPhoneNumber }}</td>
                  <td>{{ companyUserDetail.companyUserEmail }}</td>
                  <td>{{ companyUserDetail.cityName }}</td>
                  <td>{{ companyUserDetail.townName }}</td>
                  <td>{{ companyUserDetail.companyName }}</td>
                  <td>{{ companyUserDetail.companyUserPhoneNumber }}</td>
                  <td>{{ companyUserDetail.companyAdress }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
