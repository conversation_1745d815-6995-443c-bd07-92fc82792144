<div class="container-fluid mt-4">
  <div *ngIf="isSubmitting" class="d-flex justify-content-center align-items-center" style="height: 100vh;">
    <app-loading-spinner></app-loading-spinner>
  </div>

  <div class="fade-in" [class.content-blur]="isSubmitting">
    <!-- Stats Cards -->
    <div class="row mb-4">
      <div class="col-md-4">
        <div class="modern-stats-card bg-primary-light slide-in-left">
          <div class="modern-stats-icon bg-primary text-white">
            <i class="fas fa-building"></i>
          </div>
          <div class="modern-stats-info">
            <h2 class="modern-stats-value">{{ salons.length }}</h2>
            <p class="modern-stats-label">Toplam Salon</p>
          </div>
        </div>
      </div>
      <div class="col-md-4">
        <div class="modern-stats-card bg-success-light slide-in-left" style="animation-delay: 0.1s;">
          <div class="modern-stats-icon bg-success text-white">
            <i class="fas fa-map-marker-alt"></i>
          </div>
          <div class="modern-stats-info">
            <h2 class="modern-stats-value">{{ getUniqueCityCount() }}</h2>
            <p class="modern-stats-label">Farklı Şehir</p>
          </div>
        </div>
      </div>
      <div class="col-md-4">
        <div class="modern-stats-card bg-info-light slide-in-left" style="animation-delay: 0.2s;">
          <div class="modern-stats-icon bg-info text-white">
            <i class="fas fa-user-tie"></i>
          </div>
          <div class="modern-stats-info">
            <h2 class="modern-stats-value">{{ salons.length }}</h2>
            <p class="modern-stats-label">Salon Sahibi</p>
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <!-- Salon Ekleme Formu -->
      <div class="col-md-5">
        <div class="modern-card slide-in-left">
          <div class="modern-card-header">
            <h5><i class="fas fa-plus-circle me-2"></i>Salon Ekleme Paneli</h5>
          </div>
          <div class="modern-card-body">
            <form [formGroup]="unifiedForm">
              <!-- Şirket Bilgileri Bölümü -->
              <div class="form-section">
                <h6 class="form-section-title">
                  <i class="fas fa-building me-2"></i>Şirket Bilgileri
                </h6>
                <div class="mb-3">
                  <mat-form-field appearance="outline" class="modern-mat-form-field">
                    <mat-label>Şirket Adı</mat-label>
                    <input matInput formControlName="companyName" placeholder="Şirket adını giriniz">
                    <mat-icon matPrefix>business</mat-icon>
                    <mat-error *ngIf="unifiedForm.get('companyName')?.hasError('required')">
                      Şirket adı zorunludur
                    </mat-error>
                  </mat-form-field>
                </div>
                <div class="mb-3">
                  <mat-form-field appearance="outline" class="modern-mat-form-field">
                    <mat-label>Telefon Numarası</mat-label>
                    <input matInput formControlName="companyPhone" placeholder="Telefon numarasını giriniz">
                    <mat-icon matPrefix>phone</mat-icon>
                    <mat-error *ngIf="unifiedForm.get('companyPhone')?.hasError('required')">
                      Telefon numarası zorunludur
                    </mat-error>
                  </mat-form-field>
                </div>
              </div>

              <!-- Adres Bilgileri Bölümü -->
              <div class="form-section">
                <h6 class="form-section-title">
                  <i class="fas fa-map-marked-alt me-2"></i>Adres Bilgileri
                </h6>
                <div class="mb-3">
                  <mat-form-field appearance="outline" class="modern-mat-form-field">
                    <mat-label>İl</mat-label>
                    <input type="text" matInput formControlName="city" [matAutocomplete]="autoCity">
                    <mat-icon matPrefix>location_city</mat-icon>
                    <mat-autocomplete #autoCity="matAutocomplete" [displayWith]="displayCity">
                      <mat-option *ngFor="let city of filteredCities | async" [value]="city">
                        {{city.cityName}}
                      </mat-option>
                    </mat-autocomplete>
                    <mat-error *ngIf="unifiedForm.get('city')?.hasError('required')">
                      İl seçimi zorunludur
                    </mat-error>
                  </mat-form-field>
                </div>
                <div class="mb-3">
                  <mat-form-field appearance="outline" class="modern-mat-form-field">
                    <mat-label>İlçe</mat-label>
                    <input type="text" matInput formControlName="town" [matAutocomplete]="autoTown">
                    <mat-icon matPrefix>place</mat-icon>
                    <mat-autocomplete #autoTown="matAutocomplete" [displayWith]="displayTown">
                      <mat-option *ngFor="let town of filteredTowns | async" [value]="town">
                        {{town.townName}}
                      </mat-option>
                    </mat-autocomplete>
                    <mat-error *ngIf="unifiedForm.get('town')?.hasError('required')">
                      İlçe seçimi zorunludur
                    </mat-error>
                  </mat-form-field>
                </div>
                <div class="mb-3">
                  <mat-form-field appearance="outline" class="modern-mat-form-field">
                    <mat-label>Adres</mat-label>
                    <textarea matInput formControlName="address" placeholder="Adresi giriniz" rows="3"></textarea>
                    <mat-icon matPrefix>home</mat-icon>
                    <mat-error *ngIf="unifiedForm.get('address')?.hasError('required')">
                      Adres zorunludur
                    </mat-error>
                  </mat-form-field>
                </div>
              </div>

              <!-- Şirket Sahibi Bilgileri Bölümü -->
              <div class="form-section">
                <h6 class="form-section-title">
                  <i class="fas fa-user-tie me-2"></i>Şirket Sahibi Bilgileri
                </h6>
                <div class="mb-3">
                  <mat-form-field appearance="outline" class="modern-mat-form-field">
                    <mat-label>Ad Soyad</mat-label>
                    <input matInput formControlName="ownerName" placeholder="Ad soyad giriniz">
                    <mat-icon matPrefix>person</mat-icon>
                    <mat-error *ngIf="unifiedForm.get('ownerName')?.hasError('required')">
                      Ad soyad zorunludur
                    </mat-error>
                  </mat-form-field>
                </div>
                <div class="mb-3">
                  <mat-form-field appearance="outline" class="modern-mat-form-field">
                    <mat-label>Telefon Numarası</mat-label>
                    <input matInput formControlName="ownerPhone" placeholder="Telefon numarasını giriniz">
                    <mat-icon matPrefix>phone</mat-icon>
                    <mat-error *ngIf="unifiedForm.get('ownerPhone')?.hasError('required')">
                      Telefon numarası zorunludur
                    </mat-error>
                  </mat-form-field>
                </div>
                <div class="mb-3">
                  <mat-form-field appearance="outline" class="modern-mat-form-field">
                    <mat-label>E-posta</mat-label>
                    <input type="text" matInput formControlName="ownerEmail" [matAutocomplete]="autoUser" placeholder="Kullanıcı e-postasını seçiniz">
                    <mat-icon matPrefix>email</mat-icon>
                    <mat-autocomplete #autoUser="matAutocomplete" [displayWith]="displayUser">
                      <mat-option *ngFor="let user of filteredUsers | async" [value]="user">
                        {{user.email}} ({{user.firstName}} {{user.lastName}})
                      </mat-option>
                    </mat-autocomplete>
                    <mat-error *ngIf="unifiedForm.get('ownerEmail')?.hasError('required')">
                      E-posta zorunludur
                    </mat-error>
                  </mat-form-field>
                </div>
              </div>

              <button (click)="add()" class="modern-btn modern-btn-primary w-100" [disabled]="isSubmitting || !unifiedForm.valid">
                <i class="fas fa-save modern-btn-icon"></i>
                {{ isSubmitting ? 'SALON EKLENİYOR...' : 'SALONU KAYDET' }}
              </button>
            </form>
          </div>
        </div>
      </div>

      <!-- Salon Listesi -->
      <div class="col-md-7">
        <div class="modern-card slide-in-right">
          <div class="modern-card-header">
            <div class="d-flex justify-content-between align-items-center">
              <h5><i class="fas fa-list me-2"></i>Salon Listesi</h5>
              <div class="position-relative">
                <input 
                  type="text" 
                  class="modern-form-control" 
                  placeholder="Salon ara..." 
                  [(ngModel)]="searchTerm" 
                  (input)="filterSalons()">
                <i class="fas fa-search" style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%);"></i>
              </div>
            </div>
          </div>
          <div class="modern-card-body">
            <div class="table-responsive">
              <table class="modern-table">
                <thead>
                  <tr>
                    <th>
                      <div class="d-flex align-items-center">
                        <span>Şirket Adı</span>
                        <i class="fas fa-sort ms-1" style="cursor: pointer;" (click)="sortSalons('companyName')"></i>
                      </div>
                    </th>
                    <th>Şirket Sahibi</th>
                    <th>İl/İlçe</th>
                    <th>İletişim</th>
                    <th>İşlem</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let salon of filteredSalons" class="fade-in">
                    <td>
                      <div class="d-flex align-items-center">
                        <div class="salon-icon me-2">
                          <i class="fas fa-building"></i>
                        </div>
                        {{ salon.companyName }}
                      </div>
                    </td>
                    <td>{{ salon.ownerName }}</td>
                    <td>
                      <span class="modern-badge modern-badge-info">
                        <i class="fas fa-map-marker-alt me-1"></i>
                        {{ salon.cityName }}/{{ salon.townName }}
                      </span>
                    </td>
                    <td>
                      <div>{{ salon.ownerPhone }}</div>
                      <div class="text-muted small">{{ salon.ownerEmail }}</div>
                    </td>
                    <td>
                      <button class="modern-btn modern-btn-danger modern-btn-sm" (click)="deleteSalon(salon)" [disabled]="isSubmitting">
                        <i class="fas fa-trash-alt modern-btn-icon"></i>Sil
                      </button>
                    </td>
                  </tr>
                  <tr *ngIf="filteredSalons.length === 0">
                    <td colspan="5" class="text-center py-4">
                      <div class="text-muted">
                        <i class="fas fa-building fa-3x mb-3"></i>
                        <p>Henüz salon kaydı bulunmamaktadır.</p>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          <div class="modern-card-footer">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <span class="modern-badge modern-badge-primary">Toplam: {{ filteredSalons.length }} salon</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .bg-primary-light {
    background-color: var(--primary-light);
  }
  
  .bg-success-light {
    background-color: var(--success-light);
  }
  
  .bg-info-light {
    background-color: var(--info-light);
  }
  
  .bg-primary {
    background-color: var(--primary);
  }
  
  .bg-success {
    background-color: var(--success);
  }
  
  .bg-info {
    background-color: var(--info);
  }
  
  .salon-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--primary-light);
    color: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .content-blur {
    filter: blur(3px);
    pointer-events: none;
  }
  
  .form-section {
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius-md);
    padding: 1rem;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
  }
  
  .form-section:hover {
    box-shadow: var(--shadow-sm);
    transform: translateY(-2px);
  }
  
  .form-section-title {
    color: var(--primary);
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
  }
  
  .modern-mat-form-field {
    width: 100%;
  }
  
  /* Override Material styles for dark mode compatibility */
  ::ng-deep .modern-mat-form-field.mat-form-field-appearance-outline .mat-form-field-outline {
    color: var(--border-color);
  }
  
  ::ng-deep .modern-mat-form-field.mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
    color: var(--primary);
  }
  
  ::ng-deep .modern-mat-form-field .mat-form-field-label {
    color: var(--text-secondary);
  }
  
  ::ng-deep .modern-mat-form-field.mat-focused .mat-form-field-label {
    color: var(--primary);
  }
  
  ::ng-deep .modern-mat-form-field .mat-input-element {
    color: var(--text-primary);
  }
  
  ::ng-deep .modern-mat-form-field .mat-form-field-prefix {
    color: var(--primary);
    margin-right: 8px;
  }
</style>
