<div class="container mt-4">
  <!-- Loading Spinner -->
  <div
    class="d-flex justify-content-center align-items-center"
    *ngIf="isLoading"
    style="height: 100vh"
  >
    <app-loading-spinner></app-loading-spinner>
  </div>

  <div class="row" [class.content-blur]="isLoading">
    <div class="col-12">
      <div class="modern-card fade-in">
        <div class="modern-card-header">
          <h5 class="mb-0">
            <i class="fas fa-dumbbell me-2"></i>
            Üyelik Türleri
          </h5>
        
        </div>
        
        <div class="modern-card-body">
          <div class="table-container">
            <table class="modern-table">
              <thead>
                <tr>
                  <th>
                    <i class="fas fa-dumbbell me-2"></i>
                    Branş
                  </th>
                  <th>
                    <i class="fas fa-tag me-2"></i>
                    Tür
                  </th>
                  <th>
                    <i class="fas fa-calendar-day me-2"></i>
                    Süre
                    <button class="sort-btn" (click)="sortByDuration()">
                      <i class="fas" [ngClass]="sortDirection === 'asc' && activeSortField === 'duration' ? 'fa-sort-up' : 'fa-sort-down'"></i>
                    </button>
                  </th>
                  <th>
                    <i class="fas fa-money-bill-wave me-2"></i>
                    Fiyat
                  </th>
                  <th>
                    <i class="fas fa-cogs me-2"></i>
                    İşlemler
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let membershiptype of membershipTypes" class="zoom-in">
                  <td>
                    <span class="modern-badge modern-badge-info">{{ membershiptype.branch }}</span>
                  </td>
                  <td>{{ membershiptype.typeName }}</td>
                  <td>{{ getDayDisplay(membershiptype.day) }}</td>
                  <td>
                    <span class="modern-badge modern-badge-primary">{{ membershiptype.price }} ₺</span>
                  </td>
                  <td>
                    <div class="action-buttons">
                      <button 
                        type="button" 
                        class="modern-btn modern-btn-danger modern-btn-sm" 
                        (click)="deleteMembershipType(membershiptype)"
                        title="Üyelik Türünü Sil"
                      >
                        <i class="fas fa-trash-alt"></i>
                      </button>
                      <button 
                        type="button" 
                        class="modern-btn modern-btn-primary modern-btn-sm ms-2" 
                        (click)="openUpdateDialog(membershiptype)"
                        title="Üyelik Türünü Güncelle"
                      >
                        <i class="fas fa-edit"></i>
                      </button>
                    </div>
                  </td>
                </tr>
                
                <!-- Veri yoksa gösterilecek mesaj -->
                <tr *ngIf="membershipTypes.length === 0">
                  <td colspan="5" class="text-center py-4">
                    <i class="fas fa-dumbbell fa-2x mb-2 text-muted"></i>
                    <p class="mb-0">Henüz üyelik türü tanımlanmamış.</p>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
