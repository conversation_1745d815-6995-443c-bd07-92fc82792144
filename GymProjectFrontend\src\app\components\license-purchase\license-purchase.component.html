<h2 mat-dialog-title><PERSON><PERSON></h2>

<form [formGroup]="purchaseForm" (ngSubmit)="onSubmit()">
  <mat-dialog-content>
    <div *ngIf="isLoading" class="d-flex justify-content-center">
      <app-loading-spinner></app-loading-spinner>
    </div>

    <div *ngIf="!isLoading">
      <div class="row">
        <div class="col-md-12">
          <mat-form-field appearance="outline" class="w-100">
            <mat-label>Kullanıcı</mat-label>
            <input 
              type="text" 
              matInput 
              formControlName="userID" 
              [matAutocomplete]="userAuto"
              placeholder="Kullanıcı adı veya e-posta arayın"
            >
            <mat-autocomplete #userAuto="matAutocomplete" [displayWith]="displayUser">
              <mat-option *ngFor="let user of filteredUsers" [value]="user">
                {{ user.firstName }} {{ user.lastName }} ({{ user.email }})
              </mat-option>
            </mat-autocomplete>
            <mat-error *ngIf="purchaseForm.get('userID')?.hasError('required')">
              Kullanıcı seçimi zorunludur
            </mat-error>
          </mat-form-field>
        </div>
      </div>

      <div class="row">
        <div class="col-md-12">
          <mat-form-field appearance="outline" class="w-100">
            <mat-label>Lisans Paketi</mat-label>
            <input 
              type="text" 
              matInput 
              formControlName="licensePackageID" 
              [matAutocomplete]="packageAuto"
              placeholder="Lisans paketi arayın"
            >
            <mat-autocomplete #packageAuto="matAutocomplete" [displayWith]="displayPackage">
              <mat-option *ngFor="let pkg of filteredPackages" [value]="pkg">
                {{ pkg.name }} - {{ pkg.role }} ({{ pkg.durationDays }} gün) - {{ pkg.price | currency:'TRY':'symbol':'1.2-2' }}
              </mat-option>
            </mat-autocomplete>
            <mat-error *ngIf="purchaseForm.get('licensePackageID')?.hasError('required')">
              Lisans paketi seçimi zorunludur
            </mat-error>
          </mat-form-field>
        </div>
      </div>

      <div class="row">
        <div class="col-md-12">
          <mat-form-field appearance="outline" class="w-100">
            <mat-label>Ödeme Yöntemi</mat-label>
            <mat-select formControlName="paymentMethod">
              <mat-option *ngFor="let method of paymentMethods" [value]="method">
                {{ method }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="purchaseForm.get('paymentMethod')?.hasError('required')">
              Ödeme yöntemi seçimi zorunludur
            </mat-error>
          </mat-form-field>
        </div>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <button mat-button type="button" (click)="onCancel()" [disabled]="isSubmitting">İptal</button>
    <button mat-raised-button color="primary" type="submit" [disabled]="purchaseForm.invalid || isSubmitting">
      <span *ngIf="!isSubmitting">Satın Al</span>
      <div *ngIf="isSubmitting" class="spinner">
        <div class="bounce1"></div>
        <div class="bounce2"></div>
        <div class="bounce3"></div>
      </div>
    </button>
  </mat-dialog-actions>
</form>
