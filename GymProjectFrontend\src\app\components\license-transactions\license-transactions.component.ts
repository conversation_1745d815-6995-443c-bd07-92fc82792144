import { Component, OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { LicenseTransactionService } from '../../services/license-transaction.service';
import { LicensePackageService } from '../../services/license-package.service';
import { FormBuilder, FormGroup } from '@angular/forms';
import { LicensePackage } from '../../models/licensePackage';
import { LicenseTransaction } from '../../models/LicenseTransaction';
import { User } from '../../models/user';
import { UserService } from '../../services/user-service.service';

@Component({
  selector: 'app-license-transactions',
  templateUrl: './license-transactions.component.html',
  styleUrls: ['./license-transactions.component.css'],
  standalone:false
})
export class LicenseTransactionsComponent implements OnInit {
  transactions: LicenseTransaction[] = [];
  isLoading = false;
  displayedColumns: string[] = ['userName', 'packageName', 'amount', 'paymentMethod', 'transactionDate'];
  
  filterForm: FormGroup;
  users: { [id: number]: User } = {};
  packages: { [id: number]: LicensePackage } = {};
  
  constructor(
    private fb: FormBuilder,
    private licenseTransactionService: LicenseTransactionService,
    private userService: UserService,
    private licensePackageService: LicensePackageService,
    private toastr: ToastrService
  ) {
    this.filterForm = this.fb.group({
      startDate: [null],
      endDate: [null],
      userID: [null],
      paymentMethod: ['']
    });
  }

  ngOnInit(): void {
    this.loadUsers();
    this.loadLicensePackages();
    this.loadTransactions();
    
    // Apply filters when form values change
    this.filterForm.valueChanges.subscribe(() => {
      this.applyFilters();
    });
  }

  loadUsers(): void {
    this.userService.getAll().subscribe({
      next: (response) => {
        response.data.forEach(user => {
          this.users[user.userID] = user;
        });
      },
      error: (error) => {
        this.toastr.error('Kullanıcılar yüklenirken bir hata oluştu', 'Hata');
      }
    });
  }

  loadLicensePackages(): void {
    this.licensePackageService.getAll().subscribe({
      next: (response) => {
        response.data.forEach(pkg => {
          this.packages[pkg.licensePackageID] = pkg;
        });
      },
      error: (error) => {
        this.toastr.error('Lisans paketleri yüklenirken bir hata oluştu', 'Hata');
      }
    });
  }

  loadTransactions(): void {
    this.isLoading = true;
    this.licenseTransactionService.getAll().subscribe({
      next: (response) => {
        this.transactions = response.data;
        this.applyFilters();
        this.isLoading = false;
      },
      error: (error) => {
        this.toastr.error('Lisans işlemleri yüklenirken bir hata oluştu', 'Hata');
        this.isLoading = false;
      }
    });
  }

  applyFilters(): void {
    let filteredTransactions = [...this.transactions];
    const formValues = this.filterForm.value;
    
    if (formValues.startDate) {
      const startDate = new Date(formValues.startDate);
      startDate.setHours(0, 0, 0, 0);
      filteredTransactions = filteredTransactions.filter(t => 
        new Date(t.transactionDate) >= startDate
      );
    }
    
    if (formValues.endDate) {
      const endDate = new Date(formValues.endDate);
      endDate.setHours(23, 59, 59, 999);
      filteredTransactions = filteredTransactions.filter(t => 
        new Date(t.transactionDate) <= endDate
      );
    }
    
    if (formValues.userID) {
      filteredTransactions = filteredTransactions.filter(t => 
        t.userID === formValues.userID
      );
    }
    
    if (formValues.paymentMethod) {
      filteredTransactions = filteredTransactions.filter(t => 
        t.paymentMethod === formValues.paymentMethod
      );
    }
    
    this.transactions = filteredTransactions;
  }

  resetFilters(): void {
    this.filterForm.reset();
    this.loadTransactions();
  }

  getUserName(userId: number): string {
    const user = this.users[userId];
    return user ? `${user.firstName} ${user.lastName}` : 'Bilinmeyen Kullanıcı';
  }

  getUserEmail(userId: number): string {
    const user = this.users[userId];
    return user ? user.email : '';
  }

  getPackageName(packageId: number): string {
    const pkg = this.packages[packageId];
    return pkg ? pkg.name : 'Bilinmeyen Paket';
  }

  calculateTotal(): number {
    return this.transactions.reduce((sum, transaction) => sum + transaction.amount, 0);
  }
}
