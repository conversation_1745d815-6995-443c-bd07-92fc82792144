<div class="container-fluid mt-4" *ngIf="!isDialog">
  <div class="row">
    <div class="col-md-12">
      <div class="modern-card">
        <div class="card-header me-3 ms-3">
          <h5><PERSON><PERSON><PERSON><PERSON>ler<PERSON></h5>
        </div>
        <div class="card-body">
          <div class="birthday-content">
            <!-- Birthday content -->
            <div *ngIf="members.length === 0" class="no-birthdays">
              <i class="fas fa-birthday-cake fa-3x text-muted mb-3"></i>
              <p>Önümüzdeki 3 gün içinde doğum günü olan üye bulunmamaktadır.</p>
            </div>

            <div *ngIf="members.length > 0">
              <div class="members-list">
                <div class="member-card" *ngFor="let member of members">
                  <div class="member-info">
                    <div class="avatar-circle" [style.background-color]="'#4361ee'">
                      {{ member.name.charAt(0) }}
                    </div>
                    <div class="member-details">
                      <h4>{{ member.name }}</h4>
                      <p>Doğum Tarihi: {{ getFormattedBirthDate(member.birthDate) }}</p>
                      <p class="days-left">{{ getDaysUntilBirthday(member.birthDate) }} gün kaldı</p>
                    </div>
                  </div>
                  <div class="member-actions">
            <button 
              class="modern-btn modern-btn-outline-secondary" 
              (click)="openWhatsApp(member.phoneNumber)"
              title="Doğum Günü Mesajı Gönder"
            >
              <i class="fas fa-comment me-1"></i> Mesaj At
            </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Dialog version -->
<div class="birthday-panel-container" *ngIf="isDialog">
  <h2>Yaklaşan Doğum Günleri</h2>
  
  <div class="dialog-content">
    <div *ngIf="members.length === 0" class="no-birthdays">
      <i class="fas fa-birthday-cake fa-3x text-muted mb-3"></i>
      <p>Önümüzdeki 3 gün içinde doğum günü olan üye bulunmamaktadır.</p>
    </div>

    <div *ngIf="members.length > 0">
      <div class="members-list">
        <div class="member-card" *ngFor="let member of members">
          <div class="member-info">
            <div class="avatar-circle" [style.background-color]="'#4361ee'">
              {{ member.name.charAt(0) }}
            </div>
            <div class="member-details">
              <h4>{{ member.name }}</h4>
              <p>Doğum Tarihi: {{ getFormattedBirthDate(member.birthDate) }}</p>
              <p class="days-left">{{ getDaysUntilBirthday(member.birthDate) }} gün kaldı</p>
            </div>
          </div>
          <div class="member-actions">
            <button 
              class="modern-btn modern-btn-outline-secondary" 
              (click)="openWhatsApp(member.phoneNumber)"
              title="Doğum Günü Mesajı Gönder"
            >
              <i class="fas fa-comment me-1"></i> Mesaj At
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="dialog-actions">
    <button class="modern-btn modern-btn-secondary" (click)="close()">Kapat</button>
  </div>
</div>
