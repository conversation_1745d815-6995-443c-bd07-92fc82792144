<div class="main-content">
  <div class="container-fluid">
    <div class="row">
      <div class="col-md-8 offset-md-2">
        <div class="card">
          <div class="card-header">
            <h2 class="card-title">
              <i class="fas fa-lock"></i>
              {{ isFirstTimeLogin ? 'Şifrenizi Değiştirmeniz Gerekiyor' : 'Şifre Değiştirme' }}
            </h2>
          </div>

          <div class="card-body">
            <div class="alert alert-warning" *ngIf="isFirstTimeLogin">
              <i class="fas fa-info-circle"></i>
              Güvenliğiniz için, ilk girişte şifrenizi değiştirmeniz gerekmektedir. Yeni şifreniz en az 6 karakter olmalıdır.
            </div>

            <div class="alert alert-info" *ngIf="!isFirstTimeLogin">
              <i class="fas fa-info-circle"></i>
              Güvenliğiniz için şifrenizi düzenli olarak değiştirmenizi öneririz.
            </div>

            <form [formGroup]="changePasswordForm" (ngSubmit)="changePassword()" class="modern-form">
              <div class="form-row">
                <div class="form-group col-md-12">
                  <label for="currentPassword" class="modern-form-label">Mevcut Şifre</label>
                  <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-key"></i></span>
                    <input
                      id="currentPassword"
                      [type]="currentPasswordVisible ? 'text' : 'password'"
                      formControlName="currentPassword"
                      class="modern-form-control"
                      placeholder="Mevcut şifreniz"
                      [ngClass]="{'is-invalid': changePasswordForm.get('currentPassword')?.invalid && changePasswordForm.get('currentPassword')?.touched}"
                    >
                    <button type="button" class="btn btn-outline-secondary password-toggle" (click)="toggleCurrentPasswordVisibility()">
                      <i [class]="currentPasswordVisible ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
                    </button>
                  </div>
                  <div class="invalid-feedback" *ngIf="changePasswordForm.get('currentPassword')?.invalid && changePasswordForm.get('currentPassword')?.touched">
                    <span *ngIf="changePasswordForm.get('currentPassword')?.errors?.['required']">Mevcut şifre gerekli</span>
                  </div>
                </div>
              </div>

              <div class="form-row">
                <div class="form-group col-md-12">
                  <label for="newPassword" class="modern-form-label">Yeni Şifre</label>
                  <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                    <input
                      id="newPassword"
                      [type]="newPasswordVisible ? 'text' : 'password'"
                      formControlName="newPassword"
                      class="modern-form-control"
                      placeholder="Yeni şifreniz (en az 6 karakter)"
                      [ngClass]="{'is-invalid': changePasswordForm.get('newPassword')?.invalid && changePasswordForm.get('newPassword')?.touched}"
                    >
                    <button type="button" class="btn btn-outline-secondary password-toggle" (click)="toggleNewPasswordVisibility()">
                      <i [class]="newPasswordVisible ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
                    </button>
                  </div>
                  <div class="invalid-feedback" *ngIf="changePasswordForm.get('newPassword')?.invalid && changePasswordForm.get('newPassword')?.touched">
                    <span *ngIf="changePasswordForm.get('newPassword')?.errors?.['required']">Yeni şifre gerekli</span>
                    <span *ngIf="changePasswordForm.get('newPassword')?.errors?.['minlength']">Şifre en az 6 karakter olmalıdır</span>
                  </div>
                </div>
              </div>

              <div class="form-row">
                <div class="form-group col-md-12">
                  <label for="confirmPassword" class="modern-form-label">Şifre Tekrarı</label>
                  <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                    <input
                      id="confirmPassword"
                      [type]="confirmPasswordVisible ? 'text' : 'password'"
                      formControlName="confirmPassword"
                      class="modern-form-control"
                      placeholder="Yeni şifrenizi tekrar girin"
                      [ngClass]="{'is-invalid': (changePasswordForm.get('confirmPassword')?.invalid && changePasswordForm.get('confirmPassword')?.touched) || changePasswordForm.hasError('mismatch')}"
                    >
                    <button type="button" class="btn btn-outline-secondary password-toggle" (click)="toggleConfirmPasswordVisibility()">
                      <i [class]="confirmPasswordVisible ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
                    </button>
                  </div>
                  <div class="invalid-feedback" *ngIf="(changePasswordForm.get('confirmPassword')?.invalid && changePasswordForm.get('confirmPassword')?.touched) || changePasswordForm.hasError('mismatch')">
                    <span *ngIf="changePasswordForm.get('confirmPassword')?.errors?.['required']">Şifre tekrarı gerekli</span>
                    <span *ngIf="changePasswordForm.hasError('mismatch')">Şifreler eşleşmiyor</span>
                  </div>
                </div>
              </div>

              <div class="password-requirements card mt-3 mb-4">
                <div class="card-body">
                  <h5 class="card-title">Şifre Gereksinimleri:</h5>
                  <ul class="requirements-list">
                    <li [ngClass]="{'fulfilled': changePasswordForm.get('newPassword')?.value?.length >= 6}">
                      <i [class]="changePasswordForm.get('newPassword')?.value?.length >= 6 ? 'fas fa-check-circle text-success' : 'fas fa-times-circle text-danger'"></i>
                      En az 6 karakter
                    </li>
                  </ul>
                </div>
              </div>

              <div class="form-group text-center">
                <button
                  type="submit"
                  [disabled]="changePasswordForm.invalid || isLoading || changePasswordForm.hasError('mismatch')"
                  class="btn btn-primary btn-lg"
                >
                  <span *ngIf="!isLoading">Şifreyi Değiştir</span>
                  <app-loading-spinner *ngIf="isLoading" [size]="'small'" [showText]="false"></app-loading-spinner>
                </button>
              </div>
            </form>

            <div class="text-center mt-4">
              <div class="support">
                <i class="fas fa-headset"></i>
                <span>Destek: <a href="mailto:support&#64;gymkod.com">support&#64;gymkod.com</a></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>