import { Component, OnInit, Inject, Optional, Input } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Member } from '../../models/member';
import { MemberBirthday } from '../../models/member-birthday.model';
import { faComment } from '@fortawesome/free-solid-svg-icons';
import { MemberService } from '../../services/member.service';

@Component({
  selector: 'app-birthday-panel',
  templateUrl: './birthday-panel.component.html',
  styleUrls: ['./birthday-panel.component.css'],
  standalone:false
})
export class BirthdayPanelComponent implements OnInit {
  faComment = faComment;
  members: MemberBirthday[] = [];
  isDialog = false;

  constructor(
    @Optional() public dialogRef: MatDialogRef<BirthdayPanelComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: { members: MemberBirthday[] },
    private memberService: MemberService
  ) {
    this.isDialog = !!dialogRef;
  }

  ngOnInit(): void {
    if (this.isDialog && this.data) {
      // Eğer dialog olarak açıldıysa, data içindeki üyeleri kullan
      this.members = this.data.members;
    } else {
      // Eğer sayfa olarak açıldıysa, servisten üyeleri yükle
      this.loadUpcomingBirthdays();
    }
  }

  loadUpcomingBirthdays(): void {
    // Doğrudan API'den getir, önbellekleme kullanma
    this.memberService.getUpcomingBirthdays(3).subscribe({
      next: (response) => {
        if (response.success) {
          this.members = response.data;
        }
      },
      error: (error) => {
        console.error('Yaklaşan doğum günleri getirilirken hata oluştu:', error);
      }
    });
  }

  openWhatsApp(phoneNumber: string): void {
    // Format the phone number for WhatsApp
    let formattedNumber = phoneNumber.replace(/[\s\-\(\)]/g, '');
    
    // If the number doesn't start with '+' or country code, add Turkish country code
    if (!formattedNumber.startsWith('+') && !formattedNumber.startsWith('90')) {
      formattedNumber = '90' + formattedNumber;
    }
    
    // If the number starts with '0', remove it and add country code
    if (formattedNumber.startsWith('0')) {
      formattedNumber = '9' + formattedNumber;
    }
    
    // Create WhatsApp URL without any message
    const whatsappUrl = `https://wa.me/${formattedNumber}`;
    
    // Open in a new tab
    window.open(whatsappUrl, '_blank');
  }

  close(): void {
    if (this.dialogRef) {
      this.dialogRef.close();
    }
  }

  getFormattedBirthDate(birthDate: Date): string {
    const date = new Date(birthDate);
    return date.toLocaleDateString('tr-TR');
  }

  getDaysUntilBirthday(birthDate: Date): number {
    const today = new Date();
    const birth = new Date(birthDate);
    
    // Set birth year to current year
    birth.setFullYear(today.getFullYear());
    
    // If birthday has already occurred this year, set to next year
    if (birth < today) {
      birth.setFullYear(today.getFullYear() + 1);
    }
    
    // Calculate days difference
    const diffTime = Math.abs(birth.getTime() - today.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays;
  }
}
