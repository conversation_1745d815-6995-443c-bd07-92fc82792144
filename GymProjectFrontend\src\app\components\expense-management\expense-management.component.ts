import { Component, OnInit, <PERSON><PERSON><PERSON>roy, AfterViewInit, ChangeDetectorRef } from '@angular/core';
import { FormControl } from '@angular/forms';
import { ExpenseService } from '../../services/expense.service';
import { ExpenseDto } from '../../models/expenseDto.model';
import { MatDialog } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { Observable, Subject } from 'rxjs'; // Observable eklendi
import { takeUntil, debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { SingleResponseModel } from '../../models/singleResponseModel'; // SingleResponseModel eklendi
import { faEdit, faTrashAlt, faPlus, faFilter, faCalendarAlt } from '@fortawesome/free-solid-svg-icons';
import { ExpenseDialogComponent } from '../expense-dialog/expense-dialog.component';
import { DialogService } from '../../services/dialog.service';
import { Chart, registerables, ChartOptions, TooltipItem } from 'chart.js'; // Chart.js ve tipler eklendi

Chart.register(...registerables);

@Component({
  selector: 'app-expense-management',
  templateUrl:  './expense-management.component.html',
  styleUrls: ['./expense-management.component.css'],
  standalone: false
})
export class ExpenseManagementComponent implements OnInit, OnDestroy, AfterViewInit {

  expenses: ExpenseDto[] = [];
  filteredExpenses: ExpenseDto[] = [];
  isLoading = false;
  totalDailyExpense: number = 0; // Eklendi
  totalMonthlyExpense: number = 0;
  totalYearlyExpense: number = 0; // Eklendi
  selectedYear: number;
  selectedMonth: number;
  initialYear: number;
  initialMonth: number;
  years: number[] = [];
  months: { value: number, name: string }[] = [
    { value: 1, name: 'Ocak' }, { value: 2, name: 'Şubat' }, { value: 3, name: 'Mart' },
    { value: 4, name: 'Nisan' }, { value: 5, name: 'Mayıs' }, { value: 6, name: 'Haziran' },
    { value: 7, name: 'Temmuz' }, { value: 8, name: 'Ağustos' }, { value: 9, name: 'Eylül' },
    { value: 10, name: 'Ekim' }, { value: 11, name: 'Kasım' }, { value: 12, name: 'Aralık' }
  ];

  searchControl = new FormControl('');

  // Icons
  faEdit = faEdit;
  faTrashAlt = faTrashAlt;
  faPlus = faPlus;
  faFilter = faFilter;
  faCalendarAlt = faCalendarAlt;

  private destroy$ = new Subject<void>();

  // Chart instances
  distributionChart: Chart | null = null;
  trendChart: Chart | null = null;
  monthlyTrendData: { labels: string[], data: number[] } = { labels: [], data: [] }; // Aylık trend verisi için
  // trendChart: Chart | null = null; // Duplicate kaldırıldı
  initialLoadComplete = false;

  constructor(
    private expenseService: ExpenseService,
    private dialog: MatDialog,
    private toastrService: ToastrService,
    private dialogService: DialogService,
    private cdRef: ChangeDetectorRef
  ) {
    const currentDate = new Date();
    this.initialYear = currentDate.getFullYear();
    this.initialMonth = currentDate.getMonth() + 1;
    this.selectedYear = this.initialYear;
    this.selectedMonth = this.initialMonth;

    // Dinamik yıl listesi: mevcut yıldan 2 yıl sonrasına kadar, 5 yıl öncesine kadar
    const currentYear = new Date().getFullYear();
    const startYear = currentYear + 2; // 2 yıl sonrası
    const endYear = currentYear - 5;   // 5 yıl öncesi
    this.years = [];
    for (let i = startYear; i >= endYear; i--) {
      this.years.push(i);
    }
  }

  ngOnInit(): void {
    this.loadInitialTotals(); // Başlangıç toplamlarını yükle (günlük, aylık, yıllık)
    this.loadMonthlyTrendData(); // Aylık trend verisini yükle
    this.loadExpenses(); // Seçili ayın detaylarını yükle

    this.searchControl.valueChanges.pipe(
      debounceTime(750),
      distinctUntilChanged(),
      takeUntil(this.destroy$)
    ).subscribe(() => {
      this.applyFilters();
    });
  }

  ngAfterViewInit(): void {
    if (this.initialLoadComplete) {
       this.createOrUpdateCharts();
    }
  }


  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.destroyCharts();
  }

  loadExpenses(): void {
    this.isLoading = true;
    this.totalMonthlyExpense = 0;
    this.expenseService.getMonthlyExpenses(this.selectedYear, this.selectedMonth)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.expenses = response.data || []; // Veri yoksa boş dizi ata
            this.applyFilters();
            // this.calculateTotalMonthlyExpense(); // Artık loadInitialTotals veya onFilterChange içinde çağrılacak
            this.initialLoadComplete = true;
            this.createOrUpdateCharts();
          } else {
            this.toastrService.error(response.message || 'Giderler yüklenemedi.', 'Hata');
            this.expenses = [];
            this.filteredExpenses = [];
            this.createOrUpdateCharts();
          }
          this.isLoading = false;
          this.cdRef.detectChanges();
        },
        error: (error) => {
          console.error('Error fetching expenses:', error);
          this.toastrService.error('Giderler yüklenirken bir sunucu hatası oluştu.', 'Hata');
          this.expenses = [];
          this.filteredExpenses = [];
          this.isLoading = false;
          this.createOrUpdateCharts();
          this.cdRef.detectChanges();
        }
      });
  }

  calculateTotalMonthlyExpense(): void {
    this.expenseService.getTotalMonthlyExpenses(this.selectedYear, this.selectedMonth)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.totalMonthlyExpense = response.data;
          } else {
             this.totalMonthlyExpense = 0;
            // this.toastrService.warning('Aylık toplam gider hesaplanamadı.', 'Uyarı');
          }
           this.cdRef.detectChanges();
        },
        error: (error) => {
          console.error('Error fetching total monthly expense:', error);
          this.toastrService.error('Aylık toplam gider alınırken bir hata oluştu.', 'Hata');
           this.totalMonthlyExpense = 0;
           this.cdRef.detectChanges();
        }
      });
  }

  applyFilters(): void {
    const searchTerm = this.searchControl.value?.toLowerCase() || '';
    this.filteredExpenses = this.expenses.filter(expense => {
      if (!searchTerm) {
        return true; // Arama terimi yoksa tümünü dahil et
      }
      // Arama terimi varsa, Gider Türü (expenseType) de olmalı ve terimi içermeli
      return expense.expenseType && expense.expenseType.toLowerCase().includes(searchTerm);
    });
     this.createOrUpdateCharts(); // Filtreleme sonrası grafikleri güncelle
     this.cdRef.detectChanges();
  }


  onFilterChange(): void {
    // Yıl veya ay değiştiğinde hem o ayın detaylarını hem de ilgili toplamları yeniden yükle
    this.loadExpenses();
    this.calculateTotalMonthlyExpense(); // Sadece aylık toplamı güncelle
    this.loadYearlyTotal(); // Yıl değişmiş olabileceğinden yıllık toplamı güncelle
    this.loadMonthlyTrendData(); // Yıl değiştiğinde trend grafiğini güncelle
    // Günlük toplam sadece sayfa ilk açıldığında bugüne göre hesaplanır, filtre değişiminde güncellenmez.
  }

  clearFilters(): void {
    this.selectedYear = this.initialYear;
    this.selectedMonth = this.initialMonth;
    this.searchControl.setValue('');
    if (!this.searchControl.value) {
       this.loadExpenses();
    }
  }

  clearSearch(): void {
     this.searchControl.setValue('');
  }

  hasActiveFilters(): boolean {
    return !!this.searchControl.value || this.selectedYear !== this.initialYear || this.selectedMonth !== this.initialMonth;
  }

  getMonthName(monthValue: number): string {
    return this.months.find(m => m.value === monthValue)?.name || '';
  }

  getBadgeClass(expenseType: string | null | undefined): string {
    if (!expenseType) return 'modern-badge-secondary';
    const typeLower = expenseType.toLowerCase();
    if (typeLower.includes('fatura')) return 'modern-badge-info';
    if (typeLower.includes('maaş')) return 'modern-badge-primary';
    if (typeLower.includes('kira')) return 'modern-badge-warning';
    if (typeLower.includes('malzeme')) return 'modern-badge-success';
    return 'modern-badge-secondary';
  }

  openExpenseDialog(expense?: ExpenseDto): void {
    const dialogRef = this.dialog.open(ExpenseDialogComponent, {
      width: '600px',
      data: expense ? { ...expense } : null
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) { // Dialog başarıyla kapandıysa (kaydetme/güncelleme yapıldıysa)
        this.loadExpenses(); // Seçili ayın detay listesini yeniden yükle
        this.loadInitialTotals(); // Günlük, Aylık (mevcut ay), Yıllık (mevcut yıl) toplamları yeniden yükle
        this.loadMonthlyTrendData(); // Aylık trend grafiği verisini yeniden yükle
      }
    });
  }

  // --- Aylık Trend Verisini Yükleme (Optimize Edildi) ---
  loadMonthlyTrendData(): void {
    // Seçili yılın trend verilerini al
    const targetYear = this.selectedYear;

    // Tek API isteği ile tüm yılın verilerini al
    this.expenseService.getMonthlyExpenseSummary(targetYear)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response.success && response.data) {
            // Labels oluştur (01/2024, 02/2024, ...)
            const labels: string[] = [];
            const data: number[] = [];

            for (let month = 1; month <= 12; month++) {
              const label = `${month.toString().padStart(2, '0')}/${targetYear}`;
              labels.push(label);
              // Backend'den gelen data'da bu ay varsa değerini al, yoksa 0
              data.push(response.data[month] || 0);
            }

            this.monthlyTrendData = { labels, data };
            this.createOrUpdateCharts();
            this.cdRef.detectChanges();
          } else {
            this.handleMonthlyTrendError('Aylık gider trendi verisi alınamadı.');
          }
        },
        error: (error: any) => {
          console.error('Error fetching monthly trend data:', error);
          this.handleMonthlyTrendError('Aylık gider trendi verisi alınırken bir hata oluştu.');
        }
      });
  }

  private handleMonthlyTrendError(message: string): void {
    this.toastrService.error(message, 'Hata');
    // Hata durumunda boş grafik oluştur
    const targetYear = this.selectedYear;
    const labels: string[] = [];
    const data: number[] = [];

    for (let month = 1; month <= 12; month++) {
      labels.push(`${month.toString().padStart(2, '0')}/${targetYear}`);
      data.push(0);
    }

    this.monthlyTrendData = { labels, data };
    this.createOrUpdateCharts();
    this.cdRef.detectChanges();
  }

  // --- Toplamları Yükleme Metodları ---
  loadInitialTotals(): void {
    this.loadDailyTotal();
    this.calculateTotalMonthlyExpense(); // Mevcut ayın toplamı
    this.loadYearlyTotal(); // Mevcut yılın toplamı
  }

  loadDailyTotal(): void {
    const today = new Date();
    this.expenseService.getTotalDailyExpenses(today)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.totalDailyExpense = response.success ? response.data : 0;
          this.cdRef.detectChanges();
        },
        error: (error) => {
          console.error("Error fetching daily total:", error);
          this.totalDailyExpense = 0; // Hata durumunda sıfırla
          this.cdRef.detectChanges();
        }
      });
  }

  // calculateTotalMonthlyExpense metodu zaten var ve doğru çalışıyor.

  loadYearlyTotal(): void {
    this.expenseService.getTotalYearlyExpenses(this.selectedYear)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.totalYearlyExpense = response.success ? response.data : 0;
          this.cdRef.detectChanges();
        },
        error: (error) => {
          console.error("Error fetching yearly total:", error);
          this.totalYearlyExpense = 0; // Hata durumunda sıfırla
          this.cdRef.detectChanges();
        }
      });
  }


  deleteExpense(expense: ExpenseDto): void {
    // confirmGeneric yerine confirmExpenseDelete kullanıldı
    this.dialogService.confirmExpenseDelete(expense)
      .subscribe((result: boolean) => {
        if (result) {
          this.isLoading = true;
          this.expenseService.delete(expense.expenseID)
            .pipe(takeUntil(this.destroy$))
            .subscribe({
              next: (response) => {
                if (response.success) {
                  this.toastrService.success('Gider başarıyla silindi.', 'Başarılı');
                  this.loadExpenses(); // Gider listesini yenile
                  this.loadInitialTotals(); // Günlük, Aylık, Yıllık toplamları yenile
                  this.loadMonthlyTrendData(); // Aylık trend grafiğini yenile
                } else {
                  this.toastrService.error(response.message || 'Gider silinemedi.', 'Hata');
                   this.isLoading = false;
                }
              },
              error: (error) => {
                console.error('Error deleting expense:', error);
                this.toastrService.error('Gider silinirken bir sunucu hatası oluştu.', 'Hata');
                this.isLoading = false;
              }
            });
        }
      });
  }

  // --- Chart Methods ---

  destroyCharts(): void {
    this.distributionChart?.destroy();
    this.trendChart?.destroy();
    this.distributionChart = null;
    this.trendChart = null;
  }

  createOrUpdateCharts(): void {
     if (!this.initialLoadComplete || typeof document === 'undefined') return;
     this.destroyCharts();
     this.createDistributionChart();
     this.createTrendChart();
     this.cdRef.detectChanges();
  }

  private getChartColors(): { backgroundColors: string[], borderColors: string[] } {
    const isDarkMode = document.body.getAttribute('data-theme') === 'dark';
    const lightColors = ['#4361ee', '#4cc9f0', '#ffc107', '#28a745', '#dc3545', '#6c757d', '#f72585'];
    const darkColors = ['#6d8eff', '#64b5f6', '#ffb74d', '#4caf50', '#f44336', '#a0a0a0', '#ff75a0'];
    const selectedColors = isDarkMode ? darkColors : lightColors;
    const backgroundColors = selectedColors.map(color => `${color}B3`); // %70 opaklık
    const borderColors = selectedColors;
    return { backgroundColors, borderColors };
  }

  createDistributionChart(): void {
    const canvas = document.getElementById('expenseDistributionChart') as HTMLCanvasElement;
    if (!canvas) return;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const typeData = this.filteredExpenses.reduce((acc, expense) => {
      const type = expense.expenseType || 'Diğer';
      acc[type] = (acc[type] || 0) + expense.amount;
      return acc;
    }, {} as { [key: string]: number });

    const labels = Object.keys(typeData);
    const data = Object.values(typeData);
    const { backgroundColors, borderColors } = this.getChartColors();

    // Chart.js Options tipini tanımla
    const options: ChartOptions<'doughnut'> = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom',
             labels: {
               padding: 15,
               boxWidth: 12,
               usePointStyle: true,
             }
          },
          tooltip: {
            callbacks: {
              // TooltipItem tipini kullan
              label: (context: TooltipItem<'doughnut'>) => {
                let label = context.label || '';
                const value = context.parsed || 0;
                const data = context.chart.data.datasets[0].data as number[]; // Veri setini al
                const total = data.reduce((acc, current) => acc + current, 0); // Toplamı hesapla
                const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0; // Yüzdeyi hesapla

                if (label) {
                  label += ': ';
                }
                // Tutarı formatla
                label += new Intl.NumberFormat('tr-TR', { style: 'currency', currency: 'TRY' }).format(value);
                // Yüzdeyi ekle
                label += ` (${percentage}%)`;
                return label;
              }
            }
          },
          // Datalabels plugin kaldırıldığı için bu kısım silindi
        },
        animation: {
           animateScale: true,
           animateRotate: true
        }
      };

    this.distributionChart = new Chart(ctx, {
      type: 'doughnut',
      data: {
        labels: labels,
        datasets: [{
          label: 'Gider Dağılımı',
          data: data,
          backgroundColor: backgroundColors.slice(0, labels.length),
          borderColor: borderColors.slice(0, labels.length),
          borderWidth: 1,
          hoverOffset: 8
        }]
      },
      options: options // Tanımlanan options nesnesini kullan
    });
  }

  createTrendChart(): void {
    const canvas = document.getElementById('monthlyTrendChart') as HTMLCanvasElement;
    if (!canvas) return;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Aylık trend verisini kullan
    const labels = this.monthlyTrendData.labels;
    const data = this.monthlyTrendData.data;
    const { backgroundColors, borderColors } = this.getChartColors(); // Ana rengi alalım

    // Chart.js Options tipini tanımla (Çizgi grafik için 'line')
    const options: ChartOptions<'line'> = {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            // Y ekseni etiketlerini formatla
            callback: (value) => new Intl.NumberFormat('tr-TR', { style: 'currency', currency: 'TRY', maximumFractionDigits: 0 }).format(value as number)
          }
        },
        x: {
           grid: {
              display: false // X ekseni grid çizgilerini gizle
           }
        }
      },
      plugins: {
        legend: {
          display: false // Tek dataset olduğu için legend'ı gizle
        },
        tooltip: {
          mode: 'index', // Aynı X eksenindeki tüm noktaları göster
          intersect: false, // Noktanın üzerine gelmese de göster
          callbacks: {
            // TooltipItem tipini 'line' olarak kullan
            label: (context: TooltipItem<'line'>) => {
              let label = context.dataset.label || '';
              if (label) {
                label += ': ';
              }
              if (context.parsed.y !== null) {
                // Tooltip içindeki değeri formatla
                label += new Intl.NumberFormat('tr-TR', { style: 'currency', currency: 'TRY' }).format(context.parsed.y);
              }
              return label;
            }
          }
        }
      },
      hover: { // Hover ayarları
        mode: 'nearest',
        intersect: true
      },
      animation: { // Animasyon ayarları
         duration: 1000,
         easing: 'easeInOutQuad'
      }
    }; // options nesnesi burada bitmeli

    // Yeni Chart nesnesini oluştur
    this.trendChart = new Chart(ctx, {
      type: 'line', // Grafik tipi 'line'
      data: {
        labels: labels,
        datasets: [{
          label: 'Aylık Toplam Gider', // Dataset etiketi
          data: data, // Aylık veri
          borderColor: borderColors[0] || '#4361ee', // Çizgi rengi
          backgroundColor: backgroundColors[0] || 'rgba(67, 97, 238, 0.1)', // Alan dolgu rengi
          fill: true, // Alanı doldur
          tension: 0.3, // Çizgi yumuşaklığı
          pointBackgroundColor: borderColors[0] || '#4361ee', // Nokta rengi
          pointBorderColor: '#fff', // Nokta kenarlık rengi
          pointHoverBackgroundColor: '#fff', // Hover nokta rengi
          pointHoverBorderColor: borderColors[0] || '#4361ee', // Hover nokta kenarlık rengi
          pointRadius: 4, // Nokta yarıçapı
          pointHoverRadius: 6 // Hover nokta yarıçapı
        }]
      },
      options: options // Tanımlanan options nesnesini kullan
    }); // Chart nesnesi burada bitmeli
  }
}