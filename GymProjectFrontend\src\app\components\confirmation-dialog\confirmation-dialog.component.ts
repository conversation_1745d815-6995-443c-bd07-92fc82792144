import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { DialogType } from '../../models/dialog-type.enum';
import { DialogData } from '../../models/dialog.model'; 

@Component({
  selector: 'app-confirmation-dialog',
  templateUrl: './confirmation-dialog.component.html',
  standalone: false
})
export class ConfirmationDialogComponent {
  selectedPaymentMethod: string;
  DialogType = DialogType;

  constructor(
    public dialogRef: MatDialogRef<ConfirmationDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DialogData
  ) {}

  getIconClass(): string {
    switch (this.data.type) {
      case DialogType.DELETE:
        return 'danger';
      case DialogType.UPDATE:
        return 'info';
      case DialogType.PAYMENT:
        return 'success';
      case DialogType.FREEZE:
        return 'warning';
      default:
        return 'info';
    }
  }

  getIconType(): string {
    switch (this.data.type) {
      case DialogType.DELETE:
        return 'fa-trash-alt';
      case DialogType.UPDATE:
        return 'fa-edit';
      case DialogType.PAYMENT:
        return 'fa-money-bill-wave';
      case DialogType.FREEZE:
        return 'fa-snowflake';
      default:
        return 'fa-question-circle';
    }
  }
  
  getButtonIcon(): string {
    switch (this.data.type) {
      case DialogType.DELETE:
        return 'fa-trash-alt';
      case DialogType.UPDATE:
        return 'fa-save';
      case DialogType.PAYMENT:
        return 'fa-check-circle';
      case DialogType.FREEZE:
        return 'fa-snowflake';
      default:
        return 'fa-check';
    }
  }
  
  getButtonClass(): string {
    switch (this.data.type) {
      case DialogType.DELETE:
        return 'modern-btn-danger';
      case DialogType.UPDATE:
        return 'modern-btn-primary';
      case DialogType.PAYMENT:
        return 'modern-btn-success';
      case DialogType.FREEZE:
        return 'modern-btn-info';
      default:
        return 'modern-btn-primary';
    }
  }

  onYesClick(): void {
    const result = this.data.showPaymentMethods 
      ? { confirmed: true, paymentMethod: this.selectedPaymentMethod }
      : true;
    this.dialogRef.close(result);
  }

  onNoClick(): void {
    this.dialogRef.close(false);
  }
}
