import { Component, Inject, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { UserLicenseService } from '../../services/user-license.service';
import { UserLicenseDto } from '../../models/UserLicenseDto';

interface DialogData {
  userLicense: UserLicenseDto;
}

@Component({
  selector: 'app-extend-license',
  templateUrl: './extend-license.component.html',
  styleUrls: ['./extend-license.component.css'],
  standalone:false
})
export class ExtendLicenseComponent implements OnInit {
  extendForm: FormGroup;
  isSubmitting = false;
  
  constructor(
    private fb: FormBuilder,
    private userLicenseService: UserLicenseService,
    private toastr: ToastrService,
    public dialogRef: MatDialogRef<ExtendLicenseComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DialogData
  ) {
    this.extendForm = this.fb.group({
      extensionDays: [30, [Validators.required, Validators.min(1)]]
    });
  }

  ngOnInit(): void {}

  onSubmit(): void {
    if (this.extendForm.invalid) {
      this.toastr.error('Lütfen geçerli bir uzatma süresi girin', 'Hata');
      return;
    }

    this.isSubmitting = true;
    const extensionDays = this.extendForm.get('extensionDays')?.value;
    
    this.userLicenseService.extendLicense(this.data.userLicense.userLicenseID, extensionDays).subscribe({
      next: (response) => {
        this.toastr.success(response.message, 'Başarılı');
        this.dialogRef.close(true);
        this.isSubmitting = false;
      },
      error: (error) => {
        this.toastr.error('Lisans uzatılırken bir hata oluştu', 'Hata');
        this.isSubmitting = false;
      }
    });
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }
}
