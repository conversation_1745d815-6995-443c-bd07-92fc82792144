<div class="container-fluid mt-4 fade-in">
  <!-- Loading Spinner -->
  <div class="loading-overlay" *ngIf="isSubmitting">
    <div class="spinner-container">
      <app-loading-spinner></app-loading-spinner>
    </div>
  </div>

  <div class="row">
    <div class="col-md-12">
      <div class="modern-card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">
            <i class="fas fa-user-plus me-2"></i>
            <PERSON><PERSON>
          </h5>

        </div>

        <div class="card-body">
          <form [formGroup]="memberAddForm" class="fade-in">
            <!-- Form Progress Indicator -->
            <div class="progress mb-4" style="height: 6px;">
              <div
                class="progress-bar bg-primary"
                [style.width]="getFormProgress() + '%'"
                role="progressbar"
                [attr.aria-valuenow]="getFormProgress()"
                aria-valuemin="0"
                aria-valuemax="100">
              </div>
            </div>

            <!-- Personal Information Section -->
            <div class="form-section mb-4">
              <h6 class="section-title">
                <i class="fas fa-user me-2"></i>
                Kişisel Bilgiler
              </h6>

              <div class="row">
                <div class="col-md-4 mb-3">
                  <div class="modern-form-group" [ngClass]="{'has-error': memberAddForm.get('name')?.invalid && memberAddForm.get('name')?.touched}">
                    <label for="name" class="modern-form-label required">Ad Soyad</label>
                    <div class="input-group">
                      <span class="input-group-text" [ngClass]="{'text-danger': memberAddForm.get('name')?.invalid && memberAddForm.get('name')?.touched}">
                        <i class="fas fa-user"></i>
                      </span>
                      <input
                        type="text"
                        id="name"
                        formControlName="name"
                        class="modern-form-control"
                        placeholder="Ad Soyad"
                        [ngClass]="{'is-invalid': memberAddForm.get('name')?.invalid && memberAddForm.get('name')?.touched}"
                      />
                    </div>
                    <small class="error-message" *ngIf="memberAddForm.get('name')?.invalid && memberAddForm.get('name')?.touched">
                      <i class="fas fa-exclamation-circle me-1"></i>
                      Ad Soyad alanı zorunludur
                    </small>
                  </div>
                </div>

                <div class="col-md-4 mb-3">
                  <div class="modern-form-group" [ngClass]="{'has-error': memberAddForm.get('phoneNumber')?.invalid && memberAddForm.get('phoneNumber')?.touched}">
                    <label for="phoneNumber" class="modern-form-label required">Telefon Numarası</label>
                    <div class="input-group">
                      <span class="input-group-text" [ngClass]="{'text-danger': memberAddForm.get('phoneNumber')?.invalid && memberAddForm.get('phoneNumber')?.touched}">
                        <i class="fas fa-phone"></i>
                      </span>
                      <input
                        type="tel"
                        id="phoneNumber"
                        formControlName="phoneNumber"
                        class="modern-form-control"
                        placeholder="Telefon Numarası"
                        maxlength="11"
                        [ngClass]="{'is-invalid': memberAddForm.get('phoneNumber')?.invalid && memberAddForm.get('phoneNumber')?.touched}"
                      />
                    </div>
                    <small class="error-message" *ngIf="memberAddForm.get('phoneNumber')?.invalid && memberAddForm.get('phoneNumber')?.touched">
                      <i class="fas fa-exclamation-circle me-1"></i>
                      Geçerli bir telefon numarası giriniz
                    </small>
                  </div>
                </div>

                <div class="col-md-4 mb-3">
                  <div class="modern-form-group" [ngClass]="{'has-error': memberAddForm.get('gender')?.invalid && memberAddForm.get('gender')?.touched}">
                    <label for="gender" class="modern-form-label required">Cinsiyet</label>
                    <div class="input-group">
                      <span class="input-group-text" [ngClass]="{'text-danger': memberAddForm.get('gender')?.invalid && memberAddForm.get('gender')?.touched}">
                        <i class="fas fa-venus-mars"></i>
                      </span>
                      <select
                        id="gender"
                        formControlName="gender"
                        class="modern-form-control"
                        [ngClass]="{'is-invalid': memberAddForm.get('gender')?.invalid && memberAddForm.get('gender')?.touched}"
                      >
                        <option value="">Seçiniz</option>
                        <option value="1">Erkek</option>
                        <option value="2">Kadın</option>
                      </select>
                    </div>
                    <small class="error-message" *ngIf="memberAddForm.get('gender')?.invalid && memberAddForm.get('gender')?.touched">
                      <i class="fas fa-exclamation-circle me-1"></i>
                      Cinsiyet seçimi zorunludur
                    </small>
                  </div>
                </div>
              </div>
            </div>

            <!-- Additional Information Section -->
            <div class="form-section">
              <h6 class="section-title">
                <i class="fas fa-info-circle me-2"></i>
                İletişim Bilgileri
              </h6>

              <div class="row">
                <div class="col-md-4 mb-3">
                  <div class="modern-form-group">
                    <label for="birthDate" class="modern-form-label">Doğum Tarihi</label>
                    <div class="input-group">
                      <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                      <input
                        type="date"
                        id="birthDate"
                        formControlName="birthDate"
                        class="modern-form-control"
                        min="1924-12-31"
                        max="2024-12-31"
                      />
                    </div>
                  </div>
                </div>

                <div class="col-md-4 mb-3">
                  <div class="modern-form-group">
                    <label for="adress" class="modern-form-label">Adres</label>
                    <div class="input-group">
                      <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                      <input
                        type="text"
                        id="adress"
                        formControlName="adress"
                        class="modern-form-control"
                        placeholder="Adres"
                      />
                    </div>
                  </div>
                </div>

                <div class="col-md-4 mb-3">
                  <div class="modern-form-group" [ngClass]="{'has-error': memberAddForm.get('email')?.invalid && memberAddForm.get('email')?.touched}">
                    <label for="email" class="modern-form-label required">E-posta</label>
                    <div class="input-group">
                      <span class="input-group-text" [ngClass]="{'text-danger': memberAddForm.get('email')?.invalid && memberAddForm.get('email')?.touched}">
                        <i class="fas fa-envelope"></i>
                      </span>
                      <input
                        type="email"
                        id="email"
                        formControlName="email"
                        class="modern-form-control"
                        placeholder="E-posta"
                        required
                        [ngClass]="{'is-invalid': memberAddForm.get('email')?.invalid && memberAddForm.get('email')?.touched}"
                      />
                    </div>
                    <small class="error-message" *ngIf="memberAddForm.get('email')?.invalid && memberAddForm.get('email')?.touched">
                      <i class="fas fa-exclamation-circle me-1"></i>
                      Geçerli bir e-posta adresi giriniz
                    </small>
                    
                    <small class="text-warning mt-1">
                      <i class="fas fa-exclamation-triangle me-1"></i>
                      E-posta adresi, üyenin QR kodunu alabilmesi ve sisteme giriş yapabilmesi için gereklidir.
                    </small>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>

        <div class="card-footer d-flex justify-content-between align-items-center">
          <button
            class="modern-btn modern-btn-primary"
            (click)="add()"
            [disabled]="isSubmitting"
          >
            <i class="fas fa-save me-2"></i>
            {{ isSubmitting ? 'Üye Ekleniyor...' : 'Üye Ekle' }}
          </button>

          <a
            *ngIf="isProcessCompleted"
            routerLink="/membership/add"
            class="modern-btn modern-btn-success"
          >
            <i class="fas fa-id-card me-2"></i>
            Üyelik Tanımlama Paneli
          </a>
        </div>
      </div>
    </div>
  </div>
</div>
