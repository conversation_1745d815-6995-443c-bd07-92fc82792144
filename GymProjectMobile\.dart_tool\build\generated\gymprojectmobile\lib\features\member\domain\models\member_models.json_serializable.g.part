// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MembershipInfo _$MembershipInfoFromJson(Map<String, dynamic> json) =>
    MembershipInfo(
      branch: json['branch'] as String,
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      remainingDays: (json['remainingDays'] as num).toInt(),
    );

Map<String, dynamic> _$MembershipInfoToJson(MembershipInfo instance) =>
    <String, dynamic>{
      'branch': instance.branch,
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate.toIso8601String(),
      'remainingDays': instance.remainingDays,
    };

GetMemberQRByPhoneNumberDto _$GetMemberQRByPhoneNumberDtoFromJson(
  Map<String, dynamic> json,
) => GetMemberQRByPhoneNumberDto(
  name: json['name'] as String,
  scanNumber: json['scanNumber'] as String,
  remainingDays: json['remainingDays'] as String,
  memberships:
      (json['memberships'] as List<dynamic>)
          .map((e) => MembershipInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
  isFrozen: json['isFrozen'] as bool,
  freezeEndDate:
      json['freezeEndDate'] == null
          ? null
          : DateTime.parse(json['freezeEndDate'] as String),
  phoneNumber: json['phoneNumber'] as String?,
);

Map<String, dynamic> _$GetMemberQRByPhoneNumberDtoToJson(
  GetMemberQRByPhoneNumberDto instance,
) => <String, dynamic>{
  'name': instance.name,
  'scanNumber': instance.scanNumber,
  'remainingDays': instance.remainingDays,
  'memberships': instance.memberships,
  'isFrozen': instance.isFrozen,
  'freezeEndDate': instance.freezeEndDate?.toIso8601String(),
  'phoneNumber': instance.phoneNumber,
};

MemberQRResponse _$MemberQRResponseFromJson(Map<String, dynamic> json) =>
    MemberQRResponse(
      success: json['success'] as bool,
      message: json['message'] as String,
      data:
          json['data'] == null
              ? null
              : GetMemberQRByPhoneNumberDto.fromJson(
                json['data'] as Map<String, dynamic>,
              ),
    );

Map<String, dynamic> _$MemberQRResponseToJson(MemberQRResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'data': instance.data,
    };

MemberProfileDto _$MemberProfileDtoFromJson(Map<String, dynamic> json) =>
    MemberProfileDto(
      firstName: json['firstName'] as String,
      lastName: json['lastName'] as String,
      email: json['email'] as String,
      adress: json['adress'] as String?,
      birthDate:
          json['birthDate'] == null
              ? null
              : DateTime.parse(json['birthDate'] as String),
      phoneNumber: json['phoneNumber'] as String,
      profileImagePath: json['profileImagePath'] as String?,
    );

Map<String, dynamic> _$MemberProfileDtoToJson(MemberProfileDto instance) =>
    <String, dynamic>{
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'email': instance.email,
      'adress': instance.adress,
      'birthDate': instance.birthDate?.toIso8601String(),
      'phoneNumber': instance.phoneNumber,
      'profileImagePath': instance.profileImagePath,
    };

MemberProfileUpdateDto _$MemberProfileUpdateDtoFromJson(
  Map<String, dynamic> json,
) => MemberProfileUpdateDto(
  firstName: json['firstName'] as String,
  lastName: json['lastName'] as String,
  adress: json['adress'] as String?,
  birthDate: _dateStringToDateTime(json['birthDate'] as String?),
);

Map<String, dynamic> _$MemberProfileUpdateDtoToJson(
  MemberProfileUpdateDto instance,
) => <String, dynamic>{
  'firstName': instance.firstName,
  'lastName': instance.lastName,
  'adress': instance.adress,
  'birthDate': _dateTimeToDateString(instance.birthDate),
};

MemberProfileResponse _$MemberProfileResponseFromJson(
  Map<String, dynamic> json,
) => MemberProfileResponse(
  success: json['success'] as bool,
  message: json['message'] as String,
  data:
      json['data'] == null
          ? null
          : MemberProfileDto.fromJson(json['data'] as Map<String, dynamic>),
);

Map<String, dynamic> _$MemberProfileResponseToJson(
  MemberProfileResponse instance,
) => <String, dynamic>{
  'success': instance.success,
  'message': instance.message,
  'data': instance.data,
};
