// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LoginRequest _$LoginRequestFromJson(Map<String, dynamic> json) => LoginRequest(
  loginDto: LoginDto.fromJson(json['loginDto'] as Map<String, dynamic>),
  deviceInfo: json['deviceInfo'] as String,
);

Map<String, dynamic> _$LoginRequestToJson(LoginRequest instance) =>
    <String, dynamic>{
      'loginDto': instance.loginDto,
      'deviceInfo': instance.deviceInfo,
    };

LoginDto _$LoginDtoFromJson(Map<String, dynamic> json) => LoginDto(
  email: json['email'] as String,
  password: json['password'] as String,
);

Map<String, dynamic> _$LoginDtoToJson(LoginDto instance) => <String, dynamic>{
  'email': instance.email,
  'password': instance.password,
};

RegisterRequest _$RegisterRequestFromJson(Map<String, dynamic> json) =>
    RegisterRequest(
      registerDto: RegisterDto.fromJson(
        json['registerDto'] as Map<String, dynamic>,
      ),
      deviceInfo: json['deviceInfo'] as String,
    );

Map<String, dynamic> _$RegisterRequestToJson(RegisterRequest instance) =>
    <String, dynamic>{
      'registerDto': instance.registerDto,
      'deviceInfo': instance.deviceInfo,
    };

RegisterDto _$RegisterDtoFromJson(Map<String, dynamic> json) => RegisterDto(
  firstName: json['firstName'] as String,
  lastName: json['lastName'] as String,
  email: json['email'] as String,
  password: json['password'] as String,
);

Map<String, dynamic> _$RegisterDtoToJson(RegisterDto instance) =>
    <String, dynamic>{
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'email': instance.email,
      'password': instance.password,
    };

MemberRegisterRequest _$MemberRegisterRequestFromJson(
  Map<String, dynamic> json,
) => MemberRegisterRequest(
  registerDto: MemberRegisterDto.fromJson(
    json['registerDto'] as Map<String, dynamic>,
  ),
  deviceInfo: json['deviceInfo'] as String,
);

Map<String, dynamic> _$MemberRegisterRequestToJson(
  MemberRegisterRequest instance,
) => <String, dynamic>{
  'registerDto': instance.registerDto,
  'deviceInfo': instance.deviceInfo,
};

MemberRegisterDto _$MemberRegisterDtoFromJson(Map<String, dynamic> json) =>
    MemberRegisterDto(
      firstName: json['firstName'] as String,
      lastName: json['lastName'] as String,
      email: json['email'] as String,
      password: json['password'] as String,
      phoneNumber: json['phoneNumber'] as String,
    );

Map<String, dynamic> _$MemberRegisterDtoToJson(MemberRegisterDto instance) =>
    <String, dynamic>{
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'email': instance.email,
      'password': instance.password,
      'phoneNumber': instance.phoneNumber,
    };

RefreshTokenRequest _$RefreshTokenRequestFromJson(Map<String, dynamic> json) =>
    RefreshTokenRequest(
      refreshToken: json['refreshToken'] as String,
      deviceInfo: json['deviceInfo'] as String,
    );

Map<String, dynamic> _$RefreshTokenRequestToJson(
  RefreshTokenRequest instance,
) => <String, dynamic>{
  'refreshToken': instance.refreshToken,
  'deviceInfo': instance.deviceInfo,
};

AuthResponse _$AuthResponseFromJson(Map<String, dynamic> json) => AuthResponse(
  success: json['success'] as bool,
  message: json['message'] as String,
  requirePasswordChange: json['requirePasswordChange'] as bool?,
  data:
      json['data'] == null
          ? null
          : AuthData.fromJson(json['data'] as Map<String, dynamic>),
);

Map<String, dynamic> _$AuthResponseToJson(AuthResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'requirePasswordChange': instance.requirePasswordChange,
      'data': instance.data,
    };

AuthData _$AuthDataFromJson(Map<String, dynamic> json) => AuthData(
  token: json['token'] as String,
  refreshToken: json['refreshToken'] as String,
  expiration: DateTime.parse(json['expiration'] as String),
);

Map<String, dynamic> _$AuthDataToJson(AuthData instance) => <String, dynamic>{
  'token': instance.token,
  'refreshToken': instance.refreshToken,
  'expiration': instance.expiration.toIso8601String(),
};

ChangePasswordRequest _$ChangePasswordRequestFromJson(
  Map<String, dynamic> json,
) => ChangePasswordRequest(
  currentPassword: json['currentPassword'] as String,
  newPassword: json['newPassword'] as String,
);

Map<String, dynamic> _$ChangePasswordRequestToJson(
  ChangePasswordRequest instance,
) => <String, dynamic>{
  'currentPassword': instance.currentPassword,
  'newPassword': instance.newPassword,
};

LoginResponseModel _$LoginResponseModelFromJson(Map<String, dynamic> json) =>
    LoginResponseModel(
      token: json['token'] as String,
      refreshToken: json['refreshToken'] as String?,
      user: UserModel.fromJson(json['user'] as Map<String, dynamic>),
      expiration: DateTime.parse(json['expiration'] as String),
    );

Map<String, dynamic> _$LoginResponseModelToJson(LoginResponseModel instance) =>
    <String, dynamic>{
      'token': instance.token,
      'refreshToken': instance.refreshToken,
      'user': instance.user,
      'expiration': instance.expiration.toIso8601String(),
    };
