import { Component, Inject, ViewChild, ElementRef, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { MemberService } from '../../../services/member.service';

@Component({
    selector: 'app-member-update',
    templateUrl: './member-update.component.html',
    styleUrls: ['./member-update.component.css'],
    standalone: false
})
export class MemberUpdateComponent implements OnInit {
  updateForm: FormGroup;
  @ViewChild('updateButton') updateButton: ElementRef;
  isSubmitting: boolean = false;

  // Form alanları için gerekli kontroller
  requiredFields = ['name', 'phoneNumber', 'gender'];
  totalFields = 6; // Toplam form alanı sayısı

  constructor(
    public dialogRef: MatDialogRef<MemberUpdateComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private fb: FormBuilder,
    private memberService: MemberService,
    private toastrService: ToastrService
  ) {
    // Format birth date for the form if it exists
    let birthDateValue = null;

    if (data.birthDate) {
      try {
        // Convert to Date object and ensure it's valid
        const dateObj = new Date(data.birthDate);
        if (!isNaN(dateObj.getTime())) {
          // Format as YYYY-MM-DD for the date input
          birthDateValue = this.formatDateWithTimezone(dateObj);
        }
      } catch (error) {
        console.error('Error parsing birth date:', error);
      }
    }

    this.updateForm = this.fb.group({
      name: [data.name, [Validators.required, Validators.minLength(3)]],
      phoneNumber: [data.phoneNumber, [Validators.required, ]],
      gender: [this.genderTransform(data.gender), Validators.required],
      adress: [data.adress, [Validators.maxLength(200)]],
      birthDate: [birthDateValue],
      email: [data.email, [Validators.email]]
    });
  }

  ngOnInit(): void {
    // Form değişikliklerini dinle
    this.updateForm.valueChanges.subscribe(() => {
      // Form değiştiğinde ilerleme çubuğunu güncelle
      this.getFormProgress();
    });
  }



  /**
   * Form tamamlanma yüzdesini hesaplar
   * @returns Tamamlanma yüzdesi (0-100)
   */
  getFormProgress(): number {
    let filledFields = 0;
    let validRequiredFields = 0;

    // Zorunlu alanların geçerli olup olmadığını kontrol et
    for (const field of this.requiredFields) {
      if (this.updateForm.get(field)?.valid) {
        validRequiredFields++;
      }
    }

    // Doldurulmuş alanların sayısını hesapla
    Object.keys(this.updateForm.controls).forEach(key => {
      const control = this.updateForm.get(key);
      if (control?.value) {
        filledFields++;
      }
    });

    // Zorunlu alanlar %60, toplam doldurulma %40 ağırlığında
    const requiredWeight = 60;
    const filledWeight = 40;

    const requiredProgress = (validRequiredFields / this.requiredFields.length) * requiredWeight;
    const filledProgress = (filledFields / this.totalFields) * filledWeight;

    return Math.round(requiredProgress + filledProgress);
  }

  genderTransform(value: string | number): number {
    return value === 'Erkek' ? 1 : value === 'Kadın' ? 2 : value as number;
  }

  onNoClick(): void {
    this.dialogRef.close();
  }

  updateMember(): void {
    if (this.updateForm.valid) {
      this.isSubmitting = true;

      try {
        const formValue = this.updateForm.value;

        // Handle birth date properly
        let birthDateValue = formValue.birthDate;

        // If birthDate is a valid date, format it properly
        if (birthDateValue) {
          birthDateValue = this.formatDateWithTimezone(new Date(birthDateValue));
        }

        const updatedMember = {
          ...this.data,
          ...formValue,
          gender: this.genderTransform(formValue.gender),
          birthDate: birthDateValue
        };

        console.log('Sending updated member:', updatedMember);

        // E-posta adresi değişmiş mi kontrol et
        const emailChanged = updatedMember.email !== this.data.email;
        // E-posta adresi null'dan bir değere değişmiş mi kontrol et
        const emailAddedFromNull = emailChanged && !this.data.email && updatedMember.email;

        this.memberService.update(updatedMember).subscribe(
          (response: any) => {
            this.isSubmitting = false;
            if (response.success) {
              // E-posta adresi null'dan bir değere değişmişse ve telefon numarası varsa
              if (emailAddedFromNull && updatedMember.phoneNumber && updatedMember.phoneNumber.length >= 4) {
                // Telefon numarasının son 4 hanesini geçici şifre olarak kullan
                const tempPassword = updatedMember.phoneNumber.substring(updatedMember.phoneNumber.length - 4);
                this.toastrService.success(`Email adresi güncellendi. Geçici şifre: ${tempPassword}`, 'Başarılı');
              }
              // E-posta değişmiş ve kullanıcı hesabı ile ilişkilendirilmişse
              else if (emailChanged && response.message.includes('kullanıcı hesabı ile ilişkilendirildi')) {
                this.toastrService.success(response.message, 'Başarılı');
              }
              // Diğer başarılı güncelleme durumları
              else {
                this.toastrService.success(response.message, 'Başarılı');
              }
              this.dialogRef.close(true);
            } else {
              this.toastrService.error(response.message || 'Güncelleme sırasında bir hata oluştu.', 'Hata');
            }
          },
          (responseError: any) => {
            this.isSubmitting = false;
            console.error('Update error:', responseError);
            if (responseError.error && responseError.error.Errors && responseError.error.Errors.length > 0) {
              for (const error of responseError.error.Errors) {
                this.toastrService.error(error.ErrorMessage, 'Hata');
              }
            } else {
              this.toastrService.error('Güncelleme sırasında bir hata oluştu.', 'Hata');
            }
          }
        );
      } catch (error) {
        console.error('Error in updateMember:', error);
        this.isSubmitting = false;
        this.toastrService.error('Güncelleme işlemi sırasında bir hata oluştu.', 'Hata');
      }
    }
  }

  private formatDateWithTimezone(date: Date): string {
    if (!date || isNaN(date.getTime())) {
      return '';
    }

    try {
      // Format date as YYYY-MM-DD
      return date.toISOString().split('T')[0];
    } catch (error) {
      console.error('Error formatting date:', error);
      return '';
    }
  }

  // Format date for display in the UI (DD.MM.YYYY format)
  formatDateForDisplay(date: Date): string {
    if (!date || isNaN(new Date(date).getTime())) {
      return '';
    }

    try {
      const d = new Date(date);
      const day = d.getDate().toString().padStart(2, '0');
      const month = (d.getMonth() + 1).toString().padStart(2, '0');
      const year = d.getFullYear();

      return `${day}.${month}.${year}`;
    } catch (error) {
      console.error('Error formatting date for display:', error);
      return '';
    }
  }
}
