-- Üye Program Atama View Düzeltme Script
-- Schema bound view oluşturur ve index ekler

USE [GymProject]
GO

-- Mevcut view'ı drop et (eğer varsa)
IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[vw_MemberWorkoutProgramDetails]'))
BEGIN
    -- Önce index'i drop et
    IF EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[vw_MemberWorkoutProgramDetails]') AND name = N'IX_vw_MemberWorkoutProgramDetails_Clustered')
    BEGIN
        DROP INDEX [IX_vw_MemberWorkoutProgramDetails_Clustered] ON [dbo].[vw_MemberWorkoutProgramDetails]
        PRINT 'Mevcut view index silindi.'
    END
    
    DROP VIEW [dbo].[vw_MemberWorkoutProgramDetails]
    PRINT 'Mevcut view silindi.'
END
GO

-- SCHEMA BOUND VIEW oluştur (index oluşturabilmek için)
CREATE VIEW [dbo].[vw_MemberWorkoutProgramDetails] 
WITH SCHEMABINDING AS
SELECT 
    mwp.MemberWorkoutProgramID,
    mwp.MemberID,
    m.Name AS MemberName,
    m.PhoneNumber AS MemberPhone,
    mwp.WorkoutProgramTemplateID,
    wpt.ProgramName,
    wpt.Description AS ProgramDescription,
    wpt.ExperienceLevel,
    wpt.TargetGoal,
    mwp.CompanyID,
    mwp.AssignedDate,
    mwp.StartDate,
    mwp.EndDate,
    mwp.Notes,
    mwp.IsActive,
    mwp.CreationDate
FROM dbo.MemberWorkoutPrograms mwp
INNER JOIN dbo.Members m ON mwp.MemberID = m.MemberID
INNER JOIN dbo.WorkoutProgramTemplates wpt ON mwp.WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID
WHERE mwp.IsActive = 1 AND m.IsActive = 1 AND wpt.IsActive = 1
GO

-- View için UNIQUE CLUSTERED INDEX (schema bound view için gerekli)
CREATE UNIQUE CLUSTERED INDEX [IX_vw_MemberWorkoutProgramDetails_Clustered] 
ON [dbo].[vw_MemberWorkoutProgramDetails] ([MemberWorkoutProgramID])
GO

-- Performance için ek non-clustered index'ler
CREATE NONCLUSTERED INDEX [IX_vw_MemberWorkoutProgramDetails_CompanyID] 
ON [dbo].[vw_MemberWorkoutProgramDetails] ([CompanyID])
INCLUDE ([MemberID], [WorkoutProgramTemplateID], [AssignedDate])
GO

CREATE NONCLUSTERED INDEX [IX_vw_MemberWorkoutProgramDetails_MemberID] 
ON [dbo].[vw_MemberWorkoutProgramDetails] ([MemberID])
INCLUDE ([WorkoutProgramTemplateID], [AssignedDate], [StartDate], [EndDate])
GO

PRINT '=== View Düzeltme Tamamlandı ==='
PRINT 'Schema bound view oluşturuldu: vw_MemberWorkoutProgramDetails'
PRINT 'Clustered index eklendi: IX_vw_MemberWorkoutProgramDetails_Clustered'
PRINT 'Performance indexes eklendi: 2 adet'
PRINT 'View artık index destekliyor ve performans optimize edildi.'
GO
