<h2 mat-dialog-title><PERSON><PERSON></h2>

<form [formGroup]="extendForm" (ngSubmit)="onSubmit()">
  <mat-dialog-content>
    <div class="user-info mb-4">
      <h4>{{ data.userLicense.userName }}</h4>
      <p class="text-muted">{{ data.userLicense.userEmail }}</p>
      <p>
        <strong>Paket:</strong> {{ data.userLicense.packageName }}<br>
        <strong>Rol:</strong> {{ data.userLicense.role }}<br>
        <strong>Bit<PERSON><PERSON> Tarihi:</strong> {{ data.userLicense.endDate | date:'dd/MM/yyyy' }}<br>
        <strong><PERSON><PERSON>:</strong> {{ data.userLicense.remainingDays }} gün
      </p>
    </div>

    <div class="row">
      <div class="col-md-12">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>Uza<PERSON><PERSON> (gün)</mat-label>
          <input matInput type="number" formControlName="extensionDays" min="1">
          <mat-error *ngIf="extendForm.get('extensionDays')?.hasError('required')">
            Uzatma süresi zorunludur
          </mat-error>
          <mat-error *ngIf="extendForm.get('extensionDays')?.hasError('min')">
            Uzatma süresi en az 1 gün olmalıdır
          </mat-error>
        </mat-form-field>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <button mat-button type="button" (click)="onCancel()" [disabled]="isSubmitting">İptal</button>
    <button mat-raised-button color="primary" type="submit" [disabled]="extendForm.invalid || isSubmitting">
      <span *ngIf="!isSubmitting">Uzat</span>
      <div *ngIf="isSubmitting" class="spinner">
        <div class="bounce1"></div>
        <div class="bounce2"></div>
        <div class="bounce3"></div>
      </div>
    </button>
  </mat-dialog-actions>
</form>