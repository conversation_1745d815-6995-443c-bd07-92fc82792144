Stack trace:
Frame         Function      Args
0007FFFFBBB0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFAAB0) msys-2.0.dll+0x1FE8E
0007FFFFBBB0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBE88) msys-2.0.dll+0x67F9
0007FFFFBBB0  000210046832 (000210286019, 0007FFFFBA68, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFBBB0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBBB0  000210068E24 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBE90  00021006A225 (0007FFFFBBC0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFC238E0000 ntdll.dll
7FFC216A0000 KERNEL32.DLL
7FFC20DD0000 KERNELBASE.dll
7FFC22EF0000 USER32.dll
7FFC213B0000 win32u.dll
000210040000 msys-2.0.dll
7FFC23250000 GDI32.dll
7FFC20A70000 gdi32full.dll
7FFC213E0000 msvcp_win.dll
7FFC21260000 ucrtbase.dll
7FFC221F0000 advapi32.dll
7FFC21C00000 msvcrt.dll
7FFC22140000 sechost.dll
7FFC22020000 RPCRT4.dll
7FFC20180000 CRYPTBASE.DLL
7FFC20D30000 bcryptPrimitives.dll
7FFC22CA0000 IMM32.DLL
