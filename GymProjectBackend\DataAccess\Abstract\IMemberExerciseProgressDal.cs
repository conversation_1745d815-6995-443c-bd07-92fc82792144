using Core.DataAccess;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;

namespace DataAccess.Abstract
{
    /// <summary>
    /// Üye egzersiz ilerleme takip DAL interface
    /// Performance optimized for 10.000+ users
    /// </summary>
    public interface IMemberExerciseProgressDal : IEntityRepository<MemberExerciseProgress>
    {
        /// <summary>
        /// Şirket bazlı tüm egzersiz ilerlemelerini getirir (admin paneli için)
        /// </summary>
        List<MemberExerciseProgressListDto> GetCompanyProgressList(int companyId, int page = 1, int pageSize = 50);

        /// <summary>
        /// Belirli üyenin egzersiz ilerlemelerini getirir
        /// </summary>
        List<MemberExerciseProgressDto> GetMemberProgressList(int memberId, int page = 1, int pageSize = 50);

        /// <summary>
        /// Belirli üyenin belirli tarihteki il<PERSON>lemelerini getirir
        /// </summary>
        List<MemberExerciseProgressDto> GetMemberProgressByDate(int memberId, DateTime date);

        /// <summary>
        /// Üyenin program bazlı ilerlemelerini getirir
        /// </summary>
        List<MemberExerciseProgressDto> GetMemberProgressByProgram(int memberId, int memberWorkoutProgramId);

        /// <summary>
        /// Üyenin egzersiz istatistiklerini getirir
        /// </summary>
        MemberExerciseProgressStatsDto GetMemberProgressStats(int memberId);

        /// <summary>
        /// Belirli egzersizin tamamlanma durumunu kontrol eder
        /// </summary>
        ExerciseCompletionStatusDto GetExerciseCompletionStatus(int memberId, int workoutProgramExerciseId, DateTime date);

        /// <summary>
        /// Üyenin günlük antrenman ilerlemesini getirir
        /// </summary>
        DailyWorkoutProgressDto GetDailyWorkoutProgress(int memberId, DateTime date);

        /// <summary>
        /// Üyenin haftalık antrenman ilerlemesini getirir
        /// </summary>
        List<DailyWorkoutProgressDto> GetWeeklyWorkoutProgress(int memberId, DateTime startDate);

        /// <summary>
        /// Üyenin aylık antrenman ilerlemesini getirir
        /// </summary>
        List<DailyWorkoutProgressDto> GetMonthlyWorkoutProgress(int memberId, int year, int month);

        /// <summary>
        /// Belirli program için tüm egzersizlerin tamamlanma durumunu getirir (mobil API için)
        /// </summary>
        List<ExerciseCompletionStatusDto> GetProgramExerciseCompletionStatus(int memberId, int memberWorkoutProgramId, DateTime date);

        /// <summary>
        /// Aynı gün aynı egzersizin daha önce tamamlanıp tamamlanmadığını kontrol eder
        /// </summary>
        bool IsExerciseCompletedToday(int memberId, int workoutProgramExerciseId, DateTime date);

        /// <summary>
        /// Üyenin son antrenman tarihini getirir
        /// </summary>
        DateTime? GetLastWorkoutDate(int memberId);

        /// <summary>
        /// Şirket bazlı egzersiz tamamlama istatistiklerini getirir
        /// </summary>
        List<MemberExerciseProgressStatsDto> GetCompanyProgressStats(int companyId, DateTime startDate, DateTime endDate);

        /// <summary>
        /// En aktif üyeleri getirir (leaderboard için)
        /// </summary>
        List<MemberExerciseProgressStatsDto> GetTopActiveMembers(int companyId, int topCount = 10, DateTime? startDate = null);

        /// <summary>
        /// Belirli tarih aralığındaki toplam tamamlanan egzersiz sayısını getirir
        /// </summary>
        int GetTotalCompletedExercisesCount(int companyId, DateTime startDate, DateTime endDate);

        /// <summary>
        /// Rate limiting için - üyenin son progress kaydının zamanını getirir
        /// </summary>
        DateTime? GetLastProgressTime(int memberId);
    }
}
