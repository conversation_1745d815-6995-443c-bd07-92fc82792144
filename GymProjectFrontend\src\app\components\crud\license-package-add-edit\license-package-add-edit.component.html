<h2 mat-dialog-title>{{ dialogTitle }}</h2>

<form [formGroup]="licensePackageForm" (ngSubmit)="onSubmit()">
  <mat-dialog-content>
    <div class="row">
      <div class="col-md-6">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>Paket Adı</mat-label>
          <input matInput formControlName="name" placeholder="Paket adını girin">
          <mat-error *ngIf="licensePackageForm.get('name')?.hasError('required')">
            Paket adı zorunludur
          </mat-error>
        </mat-form-field>
      </div>

      <div class="col-md-6">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>Rol</mat-label>
          <mat-select formControlName="role">
            <mat-option *ngFor="let role of roles" [value]="role">
              {{ role }}
            </mat-option>
          </mat-select>
          <mat-error *ngIf="licensePackageForm.get('role')?.hasError('required')">
            Rol seçimi zorunludur
          </mat-error>
        </mat-form-field>
      </div>
    </div>

    <div class="row">
      <div class="col-md-12">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>Açıklama</mat-label>
          <textarea matInput formControlName="description" placeholder="Paket açıklamasını girin" rows="3"></textarea>
          <mat-error *ngIf="licensePackageForm.get('description')?.hasError('required')">
            Açıklama zorunludur
          </mat-error>
        </mat-form-field>
      </div>
    </div>

    <div class="row">
      <div class="col-md-6">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>Süre (gün)</mat-label>
          <input matInput type="number" formControlName="durationDays" min="1">
          <mat-error *ngIf="licensePackageForm.get('durationDays')?.hasError('required')">
            Süre zorunludur
          </mat-error>
          <mat-error *ngIf="licensePackageForm.get('durationDays')?.hasError('min')">
            Süre en az 1 gün olmalıdır
          </mat-error>
        </mat-form-field>
      </div>

      <div class="col-md-6">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>Fiyat</mat-label>
          <input matInput type="number" formControlName="price" min="0">
          <span matPrefix>₺&nbsp;</span>
          <mat-error *ngIf="licensePackageForm.get('price')?.hasError('required')">
            Fiyat zorunludur
          </mat-error>
          <mat-error *ngIf="licensePackageForm.get('price')?.hasError('min')">
            Fiyat negatif olamaz
          </mat-error>
        </mat-form-field>
      </div>
    </div>

    <div class="row">
      <div class="col-md-12">
        <mat-slide-toggle formControlName="isActive" color="primary">
          Aktif
        </mat-slide-toggle>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <button mat-button type="button" (click)="onCancel()" [disabled]="isSubmitting">İptal</button>
    <button mat-raised-button color="primary" type="submit" [disabled]="licensePackageForm.invalid || isSubmitting">
      <span *ngIf="!isSubmitting">{{ data.isEdit ? 'Güncelle' : 'Ekle' }}</span>
      <div *ngIf="isSubmitting" class="spinner">
        <div class="bounce1"></div>
        <div class="bounce2"></div>
        <div class="bounce3"></div>
      </div>
    </button>
  </mat-dialog-actions>
</form>