<div class="container-fluid mt-4">
  <div *ngIf="isLoading" class="d-flex justify-content-center align-items-center" style="height: 100vh;">
    <app-loading-spinner></app-loading-spinner>
  </div>

  <div class="fade-in" [class.content-blur]="isLoading">
    <div class="row">
      <!-- Role Stats -->
      <div class="col-md-12 mb-4">
        <div class="row">
          <div class="col-md-4">
            <div class="modern-stats-card bg-primary-light slide-in-left">
              <div class="modern-stats-icon bg-primary text-white">
                <i class="fas fa-user-shield"></i>
              </div>
              <div class="modern-stats-info">
                <h2 class="modern-stats-value">{{ operationClaims.length }}</h2>
                <p class="modern-stats-label">Toplam Rol</p>
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="modern-stats-card bg-success-light slide-in-left" style="animation-delay: 0.1s;">
              <div class="modern-stats-icon bg-success text-white">
                <i class="fas fa-users-cog"></i>
              </div>
              <div class="modern-stats-info">
                <h2 class="modern-stats-value">{{ getSystemRolesCount() }}</h2>
                <p class="modern-stats-label">Sistem Rolleri</p>
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="modern-stats-card bg-info-light slide-in-left" style="animation-delay: 0.2s;">
              <div class="modern-stats-icon bg-info text-white">
                <i class="fas fa-user-plus"></i>
              </div>
              <div class="modern-stats-info">
                <h2 class="modern-stats-value">{{ getCustomRolesCount() }}</h2>
                <p class="modern-stats-label">Özel Roller</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Add Role Form -->
      <div class="col-md-5">
        <div class="modern-card slide-in-left">
          <div class="modern-card-header">
            <h5><i class="fas fa-plus-circle me-2"></i>Yeni Rol Ekle</h5>
          </div>
          <div class="modern-card-body">
            <form [formGroup]="claimForm" (ngSubmit)="addOperationClaim()">
              <div class="modern-form-group">
                <label for="name" class="modern-form-label">Rol Adı</label>
                <div class="d-flex align-items-center">
                  <div class="input-group-text me-2">
                    <i class="fas fa-user-tag"></i>
                  </div>
                  <input 
                    type="text" 
                    class="modern-form-control" 
                    id="name" 
                    formControlName="name" 
                    placeholder="Rol adını giriniz">
                </div>
                <small class="text-danger" *ngIf="claimForm.get('name')?.invalid && claimForm.get('name')?.touched">
                  Rol adı gereklidir
                </small>
              </div>
            
              
              <button type="submit" class="modern-btn modern-btn-primary w-100 mt-4" [disabled]="!claimForm.valid || isLoading">
                <i class="fas fa-plus-circle modern-btn-icon"></i>
                {{ isLoading ? 'Ekleniyor...' : 'Rol Ekle' }}
              </button>
            </form>
          </div>
        </div>
      </div>
  
      <!-- Role List -->
      <div class="col-md-7">
        <div class="modern-card slide-in-right">
          <div class="modern-card-header">
            <div class="d-flex justify-content-between align-items-center">
              <h5><i class="fas fa-list me-2"></i>Rol Listesi</h5>
              <div class="position-relative">
                <input 
                  type="text" 
                  class="modern-form-control" 
                  placeholder="Rol ara..." 
                  [(ngModel)]="searchTerm" 
                  (input)="filterRoles()">
                <i class="fas fa-search" style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%);"></i>
              </div>
            </div>
          </div>
          <div class="modern-card-body">
            <div class="table-responsive">
              <table class="modern-table">
                <thead>
                  <tr>
                    <th>
                      <div class="d-flex align-items-center">
                        <span>Rol Adı</span>
                        <i class="fas fa-sort ms-1" style="cursor: pointer;" (click)="sortRoles('name')"></i>
                      </div>
                    </th>

                    <th>İşlem</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let claim of filteredClaims" class="fade-in">
                    <td>
                      <div class="d-flex align-items-center">
                        <div class="role-icon me-2">
                          <i class="fas fa-user-shield"></i>
                        </div>
                        {{ claim.name }}
                      </div>
                    </td>

                    <td>
                      <button class="modern-btn modern-btn-danger modern-btn-sm" (click)="deleteOperationClaim(claim)" [disabled]="isSystemRole(claim.name)">
                        <i class="fas fa-trash-alt modern-btn-icon"></i>Sil
                      </button>
                    </td>
                  </tr>
                  <tr *ngIf="filteredClaims.length === 0">
                    <td colspan="2" class="text-center py-4">
                      <div class="text-muted">
                        <i class="fas fa-search fa-3x mb-3"></i>
                        <p>Rol bulunamadı.</p>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          <div class="modern-card-footer">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <span class="modern-badge modern-badge-primary">Toplam: {{ filteredClaims.length }} rol</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .bg-primary-light {
    background-color: var(--primary-light);
  }
  
  .bg-success-light {
    background-color: var(--success-light);
  }
  
  .bg-info-light {
    background-color: var(--info-light);
  }
  
  .bg-primary {
    background-color: var(--primary);
  }
  
  .bg-success {
    background-color: var(--success);
  }
  
  .bg-info {
    background-color: var(--info);
  }
  
  .role-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--primary-light);
    color: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .content-blur {
    filter: blur(3px);
    pointer-events: none;
  }
</style>
