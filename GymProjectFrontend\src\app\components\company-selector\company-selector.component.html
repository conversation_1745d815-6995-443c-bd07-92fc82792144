<div class="container-fluid mt-4">
  <div *ngIf="isLoading" class="d-flex justify-content-center align-items-center" style="height: 100vh;">
    <app-loading-spinner></app-loading-spinner>
  </div>

  <div class="fade-in" [class.content-blur]="isLoading">
    <div class="row justify-content-center">
      <div class="col-md-8">
        <div class="modern-card slide-in-left">
          <div class="modern-card-header">
            <h5><i class="fas fa-building me-2"></i>Aktif Spor Salonu Değiştir</h5>
          </div>
          
          <div class="modern-card-body">
            <div class="modern-alert modern-alert-info mb-4 fade-in">
              <i class="fas fa-info-circle me-2"></i>
              Yönetmek istediğiniz spor salonunu seçin. Seçiminiz sonrası tüm veriler seçilen spor salonuna göre filtrelenecektir.
            </div>
            
            <div *ngIf="companies && companies.length > 0" class="mt-4 fade-in">
              <!-- Search Filter -->
              <div class="modern-form-group mb-4">
                <div class="d-flex align-items-center">
                  <div class="input-group-text me-2">
                    <i class="fas fa-search"></i>
                  </div>
                  <input 
                    type="text" 
                    class="modern-form-control" 
                    placeholder="Salon ara..." 
                    [(ngModel)]="searchTerm" 
                    (input)="filterCompanies()">
                </div>
              </div>
              
              <div class="modern-form-group">
                <label for="companySelect" class="modern-form-label">Spor Salonu Seçin:</label>
                <div class="company-list">
                  <div 
                    *ngFor="let company of filteredCompanies" 
                    class="company-card" 
                    [class.active]="selectedCompanyId === company.id"
                    (click)="selectCompany(company.id)">
                    <div class="company-icon">
                      <i class="fas fa-dumbbell"></i>
                    </div>
                    <div class="company-info">
                      <h6>{{ company.name }}</h6>
                    </div>
                  </div>
                  
                  <div *ngIf="filteredCompanies.length === 0" class="text-center py-4 text-muted">
                    <i class="fas fa-search fa-2x mb-2"></i>
                    <p>Aradığınız kriterlere uygun salon bulunamadı</p>
                  </div>
                </div>
              </div>
              
              <div class="d-flex justify-content-between mt-4">
                <button class="modern-btn modern-btn-secondary" type="button" routerLink="/allmembers">
                  <i class="fas fa-arrow-left modern-btn-icon"></i>Geri Dön
                </button>
                <button class="modern-btn modern-btn-primary" type="button" (click)="onCompanyChange(selectedCompanyId)" [disabled]="!selectedCompanyId">
                  <i class="fas fa-check-circle modern-btn-icon"></i>Değişiklikleri Uygula
                </button>
              </div>
            </div>
            
            <div *ngIf="!companies || companies.length === 0" class="modern-alert modern-alert-warning mt-4 fade-in">
              <i class="fas fa-exclamation-triangle me-2"></i>
              Erişiminiz olan spor salonu bulunamadı. Lütfen sistem yöneticisiyle iletişime geçin.
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .modern-alert {
    padding: 1rem;
    border-radius: var(--border-radius-md);
    margin-bottom: 1rem;
  }
  
  .modern-alert-info {
    background-color: var(--info-light);
    color: var(--info);
    border-left: 4px solid var(--info);
  }
  
  .modern-alert-warning {
    background-color: var(--warning-light);
    color: var(--warning);
    border-left: 4px solid var(--warning);
  }
  
  .company-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
  }
  
  .company-card {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-radius: var(--border-radius-md);
    background-color: var(--bg-secondary);
    transition: all 0.3s ease;
    cursor: pointer;
    border: 2px solid transparent;
  }
  
  .company-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-sm);
    background-color: var(--bg-tertiary);
  }
  
  .company-card.active {
    border-color: var(--primary);
    background-color: var(--primary-light);
  }
  
  .company-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-light);
    color: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 1.2rem;
  }
  
  .company-info {
    flex: 1;
  }
  
  .company-info h6 {
    margin: 0;
    font-weight: 600;
  }
  
  .content-blur {
    filter: blur(3px);
    pointer-events: none;
  }
</style>
