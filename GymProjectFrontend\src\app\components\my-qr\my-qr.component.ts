import { Component, OnInit, OnDestroy } from '@angular/core';
import { MemberService } from '../../services/member.service';
import { AuthService } from '../../services/auth.service';
import { MemberQRInfoResponse, MemberQRInfo, MembershipInfo } from '../../models/member-qr-info.model';
import { interval, Subscription } from 'rxjs';

@Component({
  selector: 'app-my-qr',
  templateUrl: './my-qr.component.html',
  styleUrls: ['./my-qr.component.css'],
  standalone: false
})
export class MyQRComponent implements OnInit, OnDestroy {
  memberInfo: MemberQRInfo | null = null;
  message: string = '';
  isError: boolean = false;
  isLoading: boolean = true;
  isSpecialMember: boolean = false;

  // QR kod yenileme için değişkenler
  qrCodeValiditySeconds: number = 300; // 5 dakika (300 saniye)
  remainingSeconds: number = 0;
  timerSubscription?: Subscription;

  constructor(
    private memberService: MemberService,
    private authService: AuthService
  ) {}

  private qrDataLoaded = false;

  ngOnInit(): void {
    // Sadece bir kez yükleme yapmak için flag kullanıyoruz
    if (!this.qrDataLoaded) {
      this.loadMemberQR();
    }
  }

  ngOnDestroy() {
    // Component yok edildiğinde timer'ı temizle
    this.clearTimer();
  }

  loadMemberQR() {
    this.isLoading = true;
    this.memberService.getMemberQRByUserId().subscribe({
      next: (response: MemberQRInfoResponse) => {
        this.qrDataLoaded = true; // Veri yüklendi, flag'i güncelle

        if (response.success) {
          this.memberInfo = response.data;
          this.isError = false;
          this.message = response.message;

          // Özel üye kontrolü - SADECE telefon numarasına göre
          const phoneNumber = this.memberInfo?.phoneNumber || '';

          // Özel üye kontrolü - SADECE '05363304276' telefon numarasına sahip üye için
          if (phoneNumber === '05363304276') {
            this.isSpecialMember = true;
            console.log('Özel üye tespit edildi!');
          } else {
            this.isSpecialMember = false;
          }

          // QR kod geçerlilik süresini başlat
          this.startQRCodeTimer();
        } else {
          this.memberInfo = null;
          this.message = response.message || 'Üyelik bilgileriniz bulunamadı.';
          this.isError = true;
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.qrDataLoaded = true; // Hata olsa bile flag'i güncelle
        this.memberInfo = null;
        this.message = error.error?.message || 'Bir hata oluştu. Lütfen daha sonra tekrar deneyin.';
        this.isError = true;
        this.isLoading = false;
        console.error('Error:', error);
      }
    });
  }

  // QR Kodunu İndir butonu kaldırıldı (QR kodları 5 dakika geçerli olduğu için)
  // downloadQRCode() {
  //   const qrCodeElement = document.querySelector('qrcode canvas');
  //   if (qrCodeElement instanceof HTMLCanvasElement) {
  //     const image = qrCodeElement.toDataURL("image/png").replace("image/png", "image/octet-stream");
  //     const link = document.createElement('a');
  //     link.download = `QR_Code_${this.memberInfo?.name || 'Member'}.png`;
  //     link.href = image;
  //     link.click();
  //   } else {
  //     console.error('QR code canvas element not found');
  //     this.message = 'QR kodu oluşturulurken bir hata oluştu. Lütfen tekrar deneyin.';
  //     this.isError = true;
  //   }
  // }

  getFutureMemberships(): MembershipInfo[] {
    if (!this.memberInfo || !this.memberInfo.memberships) return [];
    const now = new Date();
    return this.memberInfo.memberships.filter(m => new Date(m.startDate) > now);
  }

  getActiveMemberships(): MembershipInfo[] {
    if (!this.memberInfo || !this.memberInfo.memberships) return [];
    const now = new Date();
    return this.memberInfo.memberships.filter(m =>
      new Date(m.startDate) <= now && new Date(m.endDate) > now
    );
  }

  getExpiredMemberships(): MembershipInfo[] {
    if (!this.memberInfo || !this.memberInfo.memberships) return [];
    const now = new Date();
    return this.memberInfo.memberships.filter(m => new Date(m.endDate) <= now);
  }

  // Üyeliğin aktif olup olmadığını kontrol eden yardımcı fonksiyon
  hasActiveMembership(): boolean {
    // Üyelik listesi yoksa veya boşsa, aktif üyelik yok demektir
    if (!this.memberInfo || !this.memberInfo.memberships || this.memberInfo.memberships.length === 0) {
      return false;
    }

    // Aktif üyelik varsa true döndür
    return this.getActiveMemberships().length > 0;
  }

  // QR kod geçerlilik süresi için timer başlat
  startQRCodeTimer() {
    // Önceki timer'ı temizle
    this.clearTimer();

    // Süreyi başlat
    this.remainingSeconds = this.qrCodeValiditySeconds;

    // Her saniye güncellenen bir timer oluştur
    this.timerSubscription = interval(1000).subscribe(() => {
      this.remainingSeconds--;

      // Süre dolduğunda QR kodu otomatik yenile
      if (this.remainingSeconds <= 0) {
        this.refreshQRCode();
      }
    });
  }

  // Timer'ı temizle
  clearTimer() {
    if (this.timerSubscription) {
      this.timerSubscription.unsubscribe();
      this.timerSubscription = undefined;
    }
  }

  // QR kodu yenile
  refreshQRCode() {
    this.qrDataLoaded = false; // Yeniden yükleme yapabilmek için flag'i sıfırla
    this.loadMemberQR();
  }

  // Kalan süreyi dakika:saniye formatında göster
  getFormattedRemainingTime(): string {
    const minutes = Math.floor(this.remainingSeconds / 60);
    const seconds = this.remainingSeconds % 60;
    return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
  }

  // Kalan süre yüzdesini hesapla (progress bar için)
  getRemainingTimePercentage(): number {
    return (this.remainingSeconds / this.qrCodeValiditySeconds) * 100;
  }
}