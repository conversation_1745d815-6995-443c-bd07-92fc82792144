import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';
import { MemberService } from '../../services/member.service';
import { ProductService } from '../../services/product.service';
import { TransactionService } from '../../services/transaction.service';
import { ToastrService } from 'ngx-toastr';
import { Member } from '../../models/member';
import { Product } from '../../models/product';
import { Observable } from 'rxjs';
import { map, startWith } from 'rxjs/operators';

interface CartItem {
  productId: number;
  quantity: number;
  unitPrice: number;
}

@Component({
    selector: 'app-product-sale',
    templateUrl: './product-sale.component.html',
    styleUrls: ['./product-sale.component.css'],
    standalone: false
})
export class ProductSaleComponent implements OnInit {
  saleForm: FormGroup;
  members: Member[] = [];
  products: Product[] = [];
  filteredMembers: Observable<Member[]>;
  filteredProducts: Observable<Product[]>;
  isLoading: boolean = false;
  cartItems: CartItem[] = [];
  totalAmount: number = 0;
  memberSelectionInvalid: boolean = false;
  productSelectionInvalid: boolean = false;

  constructor(
    private fb: FormBuilder,
    private memberService: MemberService,
    private productService: ProductService,
    private transactionService: TransactionService,
    private toastrService: ToastrService
  ) {}

  ngOnInit(): void {
    this.createSaleForm();
    this.getMembers();
    this.getProducts(); // getProducts çağrısı burada

    this.filteredMembers = this.saleForm.get('member')!.valueChanges.pipe(
      startWith(''),
      map(value => typeof value === 'string' ? value : value?.name ?? ''),
      map(name => name ? this._filterMembers(name) : this.members.slice())
    );

    // Ürün filtreleme için eklendi
    this.filteredProducts = this.saleForm.get('product')!.valueChanges.pipe(
      startWith(''), // Başlangıçta boş string ile başlar
      map(value => typeof value === 'string' ? value : value?.name ?? ''), // Gelen değeri string'e çevirir
      map(name => name ? this._filterProducts(name) : this.products.slice()) // İsim varsa filtreler, yoksa tüm ürünleri döner
    );
  }

  createSaleForm() {
    this.saleForm = this.fb.group({
      member: ['', Validators.required],
      product: [''],
      quantity: [1, [Validators.required, Validators.min(1)]]
    });
  }

  addToCart() {
    this.validateProductSelection();

    const selectedProduct = this.saleForm.get('product')?.value;
    const quantity = this.saleForm.get('quantity')?.value;

    if (!this.productSelectionInvalid && selectedProduct && typeof selectedProduct !== 'string' && quantity) {
      const cartItem: CartItem = {
        productId: selectedProduct.productID,
        quantity: quantity,
        unitPrice: selectedProduct.price
      };

      this.cartItems.push(cartItem);
      this.calculateTotal();
      this.saleForm.patchValue({ product: '', quantity: 1 });
      this.saleForm.get('product')?.markAsUntouched();
      this.productSelectionInvalid = false;
    } else if (this.productSelectionInvalid || typeof selectedProduct === 'string') {
        this.toastrService.error('Lütfen listeden geçerli bir ürün seçiniz', 'Hata');
        if (typeof selectedProduct === 'string') {
            this.productSelectionInvalid = true;
            this.saleForm.get('product')?.setErrors({ 'invalidSelection': true });
        }
    } else if (!selectedProduct) {
         this.toastrService.error('Lütfen bir ürün seçiniz', 'Hata');
    } else if (!quantity || quantity < 1) {
         this.toastrService.error('Lütfen geçerli bir miktar giriniz', 'Hata');
    }
  }


  removeFromCart(index: number) {
    this.cartItems.splice(index, 1);
    this.calculateTotal();
  }

  getProductName(productId: number): string {
    const product = this.products.find(p => p.productID === productId);
    return product ? product.name : '';
  }

  calculateTotal() {
    this.totalAmount = this.cartItems.reduce((sum, item) =>
      sum + (item.unitPrice * item.quantity), 0);
  }

  sell() {
    this.validateMemberSelection();

    if (!this.memberSelectionInvalid && this.cartItems.length > 0) {
      this.isLoading = true;
      const selectedMember = this.saleForm.get('member')?.value;

      const bulkTransaction = {
        memberID: selectedMember.memberID,
        transactionType: 'Satış',
        items: this.cartItems.map(item => ({
          productID: item.productId,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          amount: item.quantity * item.unitPrice
        }))
      };

      this.transactionService.addBulkTransaction(bulkTransaction).subscribe({
        next: (response) => {
          this.toastrService.success('Ürün satışı başarılı', 'Başarılı');
          this.resetForm();
          this.isLoading = false;
        },
        error: (error) => {
          this.toastrService.error('Ürün satışı başarısız', 'Hata');
          console.error("Satış hatası:", error);
          this.isLoading = false;
        }
      });
    } else if (this.memberSelectionInvalid) {
      this.toastrService.error('Lütfen listeden geçerli bir üye seçiniz', 'Hata');
    } else if (this.cartItems.length === 0) {
      this.toastrService.error('Sepetiniz boş', 'Hata');
    }
  }

  displayMember(member: Member): string {
    return member && member.name ? member.name : '';
  }

  displayProduct(product: Product): string {
    return product && product.name ? `${product.name} (${product.price}₺)` : '';
  }

  private _filterMembers(name: string): Member[] {
    const filterValue = name.toLowerCase();
    return this.members.filter(member =>
      member.name.toLowerCase().includes(filterValue) ||
      member.phoneNumber.includes(filterValue)
    );
  }

  private _filterProducts(name: string): Product[] {
    const filterValue = name.toLowerCase();
    // Eğer filtre değeri boşsa tüm ürünleri döndür, değilse filtrele
    if (!filterValue) {
        return this.products.slice();
    }
    return this.products.filter(product =>
      product.name.toLowerCase().includes(filterValue)
    );
  }


  getMembers() {
    this.memberService.getMembers().subscribe(response => {
      this.members = response.data;
      // Üye listesi yüklendikten sonra filtrelemeyi tetikle (opsiyonel)
      // this.saleForm.get('member')?.updateValueAndValidity();
    });
  }

  getProducts() {
    this.productService.getProducts().subscribe(response => {
      this.products = response.data;
      // Ürün listesi yüklendikten sonra filtrelemeyi tetikle
      // Mevcut değeri (genellikle boş string veya null) kullanarak observable'ı yeniden çalıştırır
      // Bu, startWith('')'in ürünler yüklenmeden önce çalışması durumunda listeyi garantiler.
      this.saleForm.get('product')?.updateValueAndValidity({ onlySelf: true, emitEvent: true });
    });
  }

  resetForm() {
    this.saleForm.reset({
      member: '',
      product: '',
      quantity: 1
    });
    this.saleForm.get('member')?.setValue('');
    this.saleForm.get('product')?.setValue('');
    this.cartItems = [];
    this.totalAmount = 0;
    this.memberSelectionInvalid = false;
    this.productSelectionInvalid = false;
  }

  validateMemberSelection() {
    const memberValue = this.saleForm.get('member')?.value;

    if (typeof memberValue === 'string' || !memberValue || !memberValue.memberID) {
      this.memberSelectionInvalid = true;
      if (typeof memberValue === 'string' && memberValue.trim() !== '') {
         this.saleForm.get('member')?.setErrors({ 'invalidSelection': true });
      }
    } else {
      this.memberSelectionInvalid = false;
      if (this.saleForm.get('member')?.hasError('invalidSelection')) {
          const errors = this.saleForm.get('member')?.errors;
          if (errors) {
              delete errors['invalidSelection'];
              this.saleForm.get('member')?.setErrors(Object.keys(errors).length > 0 ? errors : null);
          }
      }
    }
  }

  validateProductSelection() {
    const productValue = this.saleForm.get('product')?.value;

    if (typeof productValue === 'string' || (productValue && !productValue.productID)) {
        if (typeof productValue === 'string' && productValue.trim() !== '') {
            this.productSelectionInvalid = true;
            this.saleForm.get('product')?.setErrors({ 'invalidSelection': true });
        }
        else if (productValue && !productValue.productID) {
             this.productSelectionInvalid = true;
             this.saleForm.get('product')?.setErrors({ 'invalidSelection': true });
        }
        else {
            this.productSelectionInvalid = false;
            if (this.saleForm.get('product')?.hasError('invalidSelection')) {
                const errors = this.saleForm.get('product')?.errors;
                if (errors) {
                    delete errors['invalidSelection'];
                    this.saleForm.get('product')?.setErrors(Object.keys(errors).length > 0 ? errors : null);
                }
            }
        }

    } else {
        this.productSelectionInvalid = false;
        if (this.saleForm.get('product')?.hasError('invalidSelection')) {
            const errors = this.saleForm.get('product')?.errors;
            if (errors) {
                delete errors['invalidSelection'];
                this.saleForm.get('product')?.setErrors(Object.keys(errors).length > 0 ? errors : null);
            }
        }
    }
  }

}