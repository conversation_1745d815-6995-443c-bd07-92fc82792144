<!-- license-transactions.component.html -->
<div class="container mt-4">
    <div class="card">
      <div class="card-header">
        <h3>Lisans İşlem Geçmişi</h3>
      </div>
  
      <div class="card-body">
        <div class="filter-section mb-4">
          <form [formGroup]="filterForm">
            <div class="row">
              <div class="col-md-3">
                <mat-form-field appearance="outline" class="w-100">
                  <mat-label>Başlangıç Tarihi</mat-label>
                  <input matInput [matDatepicker]="startPicker" formControlName="startDate">
                  <mat-datepicker-toggle matSuffix [for]="startPicker"></mat-datepicker-toggle>
                  <mat-datepicker #startPicker></mat-datepicker>
                </mat-form-field>
              </div>
  
              <div class="col-md-3">
                <mat-form-field appearance="outline" class="w-100">
                  <mat-label><PERSON><PERSON><PERSON></mat-label>
                  <input matInput [matDatepicker]="endPicker" formControlName="endDate">
                  <mat-datepicker-toggle matSuffix [for]="endPicker"></mat-datepicker-toggle>
                  <mat-datepicker #endPicker></mat-datepicker>
                </mat-form-field>
              </div>
  
              <div class="col-md-3">
                <mat-form-field appearance="outline" class="w-100">
                  <mat-label>Kullanıcı</mat-label>
                  <mat-select formControlName="userID">
                    <mat-option [value]="null">Tümü</mat-option>
                    <mat-option *ngFor="let user of users | keyvalue" [value]="user.key">
                      {{ user.value.firstName }} {{ user.value.lastName }}
                    </mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
  
              <div class="col-md-3">
                <mat-form-field appearance="outline" class="w-100">
                  <mat-label>Ödeme Yöntemi</mat-label>
                  <mat-select formControlName="paymentMethod">
                    <mat-option [value]="''">Tümü</mat-option>
                    <mat-option value="Nakit">Nakit</mat-option>
                    <mat-option value="Kredi Kartı">Kredi Kartı</mat-option>
                    <mat-option value="Havale/EFT">Havale/EFT</mat-option>
                    <mat-option value="Uzatma">Uzatma</mat-option>
                  </mat-select>
                </mat-form-field>
              </div>
            </div>
  
            <div class="row">
              <div class="col-md-12 text-end">
                <button mat-raised-button type="button" (click)="resetFilters()">
                  <i class="fas fa-undo me-2"></i>Filtreleri Sıfırla
                </button>
              </div>
            </div>
          </form>
        </div>
  
        <div *ngIf="isLoading" class="d-flex justify-content-center">
          <app-loading-spinner></app-loading-spinner>
        </div>
  
        <div *ngIf="!isLoading && transactions.length === 0" class="alert alert-info">
          Lisans işlemi bulunamadı veya filtrelere uygun işlem yok.
        </div>
  
        <div *ngIf="!isLoading && transactions.length > 0" class="table-responsive">
          <table mat-table [dataSource]="transactions" class="w-100">
            <!-- User Name Column -->
            <ng-container matColumnDef="userName">
              <th mat-header-cell *matHeaderCellDef>Kullanıcı</th>
              <td mat-cell *matCellDef="let item">
                {{ getUserName(item.userID) }}<br>
                <small class="text-muted">{{ getUserEmail(item.userID) }}</small>
              </td>
            </ng-container>
  
            <!-- Package Name Column -->
            <ng-container matColumnDef="packageName">
              <th mat-header-cell *matHeaderCellDef>Paket</th>
              <td mat-cell *matCellDef="let item">{{ getPackageName(item.licensePackageID) }}</td>
            </ng-container>
  
            <!-- Amount Column -->
            <ng-container matColumnDef="amount">
              <th mat-header-cell *matHeaderCellDef>Tutar</th>
              <td mat-cell *matCellDef="let item">{{ item.amount | currency:'TRY':'symbol':'1.2-2' }}</td>
            </ng-container>
  
            <!-- Payment Method Column -->
            <ng-container matColumnDef="paymentMethod">
              <th mat-header-cell *matHeaderCellDef>Ödeme Yöntemi</th>
              <td mat-cell *matCellDef="let item">{{ item.paymentMethod }}</td>
            </ng-container>
  
            <!-- Transaction Date Column -->
            <ng-container matColumnDef="transactionDate">
              <th mat-header-cell *matHeaderCellDef>İşlem Tarihi</th>
              <td mat-cell *matCellDef="let item">{{ item.transactionDate | date:'dd/MM/yyyy HH:mm' }}</td>
            </ng-container>
  
            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
          </table>
  
          <div class="mt-3 text-end">
            <strong>Toplam:</strong> {{ calculateTotal() | currency:'TRY':'symbol':'1.2-2' }}
          </div>
        </div>
      </div>
    </div>
  </div>