// license-dashboard.component.ts
import { Component, OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { LicensePackageService } from '../../services/license-package.service';
import { UserLicenseService } from '../../services/user-license.service';
import { LicenseTransactionService } from '../../services/license-transaction.service';
import { AuthService } from '../../services/auth.service';
import { UserModel } from '../../models/userModel';
import { LicenseTransaction } from '../../models/LicenseTransaction';
import { UserLicenseDto } from '../../models/UserLicenseDto';

@Component({
  selector: 'app-license-dashboard',
  templateUrl: './license-dashboard.component.html',
  styleUrls: ['./license-dashboard.component.css'],
  standalone:false
})
export class LicenseDashboardComponent implements OnInit {
  isLoading = false;
  currentUser: UserModel | null = null;
  userLicenses: UserLicenseDto[] = [];
  
  // Statistics
  totalActiveLicenses = 0;
  expiringLicenses = 0; // Licenses expiring in 7 days
  recentTransactions: LicenseTransaction[] = [];
  totalRevenue = 0;

  // Computed properties
  get expiringLicensesList(): UserLicenseDto[] {
    return this.userLicenses.filter(l => l.remainingDays > 0 && l.remainingDays <= 7);
  }

  get hasExpiringLicenses(): boolean {
    return this.expiringLicensesList.length > 0;
  }
  
  constructor(
    private licensePackageService: LicensePackageService,
    private userLicenseService: UserLicenseService,
    private licenseTransactionService: LicenseTransactionService,
    private authService: AuthService,
    private toastr: ToastrService
  ) {}

  ngOnInit(): void {
    this.currentUser = this.authService.currentUserValue;
    this.loadDashboardData();
  }

  loadDashboardData(): void {
    this.isLoading = true;
    
    // Load active licenses
    this.userLicenseService.getAll().subscribe({
      next: (response) => {
        this.userLicenses = response.data;
        this.totalActiveLicenses = this.userLicenses.length;
        this.expiringLicenses = this.expiringLicensesList.length;
        this.isLoading = false;
      },
      error: (error) => {
        this.toastr.error('Lisans bilgileri yüklenirken bir hata oluştu', 'Hata');
        this.isLoading = false;
      }
    });
    
    // Load recent transactions
    this.licenseTransactionService.getAll().subscribe({
      next: (response) => {
        // Sort by date descending and take first 5
        this.recentTransactions = response.data
          .sort((a, b) => new Date(b.transactionDate).getTime() - new Date(a.transactionDate).getTime())
          .slice(0, 5);
        
        // Calculate total revenue from all transactions
        this.totalRevenue = response.data.reduce((sum, transaction) => sum + transaction.amount, 0);
      },
      error: (error) => {
        this.toastr.error('İşlem bilgileri yüklenirken bir hata oluştu', 'Hata');
      }
    });
  }

  getUserLicenses(): void {
    if (this.currentUser) {
      this.userLicenseService.getActiveByUserId(parseInt(this.currentUser.nameidentifier)).subscribe({
        next: (response) => {
          this.userLicenses = response.data;
        },
        error: (error) => {
          this.toastr.error('Lisans bilgileri yüklenirken bir hata oluştu', 'Hata');
        }
      });
    }
  }
  
  getRemainingDaysClass(days: number): string {
    if (days <= 0) {
      return 'text-danger';
    } else if (days <= 7) {
      return 'text-warning';
    } else {
      return 'text-success';
    }
  }

  getBadgeClass(days: number): string {
    if (days <= 0) {
      return 'badge bg-danger';
    } else if (days <= 7) {
      return 'badge bg-warning';
    } else {
      return 'badge bg-success';
    }
  }

  getLicenseStatus(days: number): string {
    return days > 0 ? 'Aktif' : 'Süresi Dolmuş';
  }
}