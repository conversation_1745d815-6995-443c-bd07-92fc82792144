/* Member Add Component Styles */

/* Loading Spinner */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(3px);
}

.spinner-container {
  text-align: center;
}

/* Fade In Animation */
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* Card Styles */
.modern-card {
  border-radius: 0.75rem;
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  transition: all 0.3s ease;
  overflow: hidden;
  background-color: var(--card-bg-color);
}

.modern-card:hover {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
  background-color: transparent;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-body {
  padding: 1.5rem;
}

.card-footer {
  background-color: transparent;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding: 1rem 1.5rem;
}

/* Form Styles */
.form-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

.form-section:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

.section-title {
  margin-bottom: 1.25rem;
  font-weight: 600;
  color: var(--primary-color);
  display: flex;
  align-items: center;
}

.modern-form-group {
  margin-bottom: 1rem;
  position: relative;
}

/* Hata durumunda form grubu */
.modern-form-group.has-error {
  animation: highlight 1s ease-in-out;
}

@keyframes highlight {
  0% { background-color: rgba(220, 53, 69, 0.1); }
  100% { background-color: transparent; }
}

/* Hata mesajı için stil */
.error-message {
  color: #dc3545;
  font-size: 0.85rem;
  margin-top: 0.25rem;
  display: block;
  animation: fadeIn 0.3s ease-in-out;
}

.modern-form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-color);
}

/* Zorunlu alan etiketi için stil */
.modern-form-label.required::after {
  content: '*';
  color: #dc3545;
  margin-left: 4px;
}

/* Hata durumunda etiket rengi */
.form-error .modern-form-label {
  color: #dc3545;
  font-weight: 600;
}

.modern-form-control {
  display: block;
  width: 100%;
  padding: 0.5rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  color: var(--input-text);
  background-color: var(--input-bg);
  background-clip: padding-box;
  border: 1px solid var(--input-border);
  border-radius: 0.5rem;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

/* Zorunlu alanlar için hata durumu */
.modern-form-control.ng-invalid.ng-touched {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* Titreşim animasyonu için sınıf */
.shake-animation {
  animation: shake 0.6s cubic-bezier(.36,.07,.19,.97) both;
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  perspective: 1000px;
  border-color: #dc3545 !important;
  box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.5) !important;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-6px); }
  20%, 40%, 60%, 80% { transform: translateX(6px); }
}

.modern-form-control:focus {
  border-color: var(--primary-color);
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
}

.input-group {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  width: 100%;
}

.input-group-text {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: var(--text-muted);
  text-align: center;
  white-space: nowrap;
  background-color: var(--input-bg);
  border: 1px solid var(--input-border);
  border-radius: 0.5rem 0 0 0.5rem;
  border-right: none;
}

.input-group .modern-form-control {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  position: relative;
  flex: 1 1 auto;
  width: 1%;
  min-width: 0;
}

/* Button Styles */
.modern-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  font-weight: 500;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  gap: 0.5rem;
}

.modern-btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.modern-btn-primary:hover {
  background-color: var(--secondary-color);
  transform: translateY(-2px);
}

.modern-btn-primary:disabled {
  background-color: #a0aec0;
  cursor: not-allowed;
  transform: none;
}

.modern-btn-success {
  background-color: #28a745;
  color: white;
}

.modern-btn-success:hover {
  background-color: #218838;
  transform: translateY(-2px);
}

.modern-btn-warning {
  background-color: #ffc107;
  color: #212529;
}

.modern-btn-warning:hover {
  background-color: #e0a800;
  transform: translateY(-2px);
}

.modern-btn-outline-primary {
  background-color: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.modern-btn-outline-primary:hover {
  background-color: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}

/* Progress Bar */
.progress {
  display: flex;
  height: 6px;
  overflow: hidden;
  font-size: 0.75rem;
  background-color: #e9ecef;
  border-radius: 0.25rem;
}

.progress-bar {
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  background-color: var(--primary-color);
  transition: width 0.6s ease;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .form-section {
    background-color: rgba(255, 255, 255, 0.05);
  }

  .form-section:hover {
    background-color: rgba(255, 255, 255, 0.08);
  }

  .input-group-text {
    background-color: var(--bg-tertiary);
    border-color: var(--border-color);
    color: var(--text-secondary);
  }

  .progress {
    background-color: #4a5568;
  }

  .modern-btn-outline-primary {
    border-color: var(--primary-color);
  }

  .modern-btn-primary:disabled {
    background-color: #4a5568;
  }
}

/* Responsive Adjustments */
@media (max-width: 767.98px) {
  .card-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .card-header button {
    margin-top: 1rem;
    width: 100%;
  }

  .card-footer {
    flex-direction: column;
    gap: 1rem;
  }

  .card-footer button,
  .card-footer a {
    width: 100%;
  }
}
