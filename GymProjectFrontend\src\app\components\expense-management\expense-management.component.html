<div class="container-fluid mt-4 fade-in">
  <!-- Loading Spinner -->
  <div class="loading-overlay" *ngIf="isLoading">
    <div class="spinner-container">
      <app-loading-spinner></app-loading-spinner>
    </div>
  </div>

  <div class="row" [class.content-blur]="isLoading">
    <!-- Summary Cards Row (Günlük, Aylık, Yıllık) -->
    <div class="col-md-12 mb-4">
      <div class="row">
        <!-- Daily Total Expense Card -->
        <div class="col-md-4 mb-3">
          <div class="modern-stats-card daily-expense-card"> <!-- Renk için yeni class -->
            <div class="modern-stats-icon">
              <i class="fas fa-calendar-day"></i> <!-- <PERSON><PERSON> değişti -->
            </div>
            <div class="modern-stats-info">
              <h3 class="modern-stats-value">{{ totalDailyExpense | currency:'₺':'symbol':'1.2-2':'tr' }}</h3>
              <p class="modern-stats-label">Bugünkü Toplam Gider</p>
            </div>
          </div>
        </div>
        <!-- Monthly Total Expense Card -->
        <div class="col-md-4 mb-3">
          <div class="modern-stats-card monthly-expense-card"> <!-- Renk için yeni class -->
            <div class="modern-stats-icon">
              <i class="fas fa-calendar-alt"></i> <!-- İkon değişti -->
            </div>
            <div class="modern-stats-info">
              <h3 class="modern-stats-value">{{ totalMonthlyExpense | currency:'₺':'symbol':'1.2-2':'tr' }}</h3>
              <p class="modern-stats-label">{{ getMonthName(selectedMonth) }} {{ selectedYear }} Toplam Gider</p> <!-- Ay adı eklendi -->
            </div>
          </div>
        </div>
         <!-- Yearly Total Expense Card -->
         <div class="col-md-4 mb-3">
          <div class="modern-stats-card yearly-expense-card"> <!-- Renk için yeni class -->
            <div class="modern-stats-icon">
              <i class="fas fa-calendar-week"></i> <!-- İkon değişti -->
            </div>
            <div class="modern-stats-info">
              <h3 class="modern-stats-value">{{ totalYearlyExpense | currency:'₺':'symbol':'1.2-2':'tr' }}</h3>
              <p class="modern-stats-label">{{ selectedYear }} Yılı Toplam Gider</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Charts Row -->
    <div class="col-md-12 mb-4">
      <div class="row">
        <!-- Expense Distribution Chart -->
        <div class="col-md-6 mb-4">
          <div class="modern-card h-100">
            <div class="modern-card-header">
              <h5 class="mb-0">Gider Dağılımı (Türe Göre)</h5>
            </div>
            <div class="modern-card-body">
              <div class="chart-container" style="position: relative; height: 300px;">
                <canvas id="expenseDistributionChart"></canvas>
                <div *ngIf="!isLoading &amp;&amp; filteredExpenses.length === 0 &amp;&amp; distributionChart" class="chart-empty-state">
                  Veri Yok
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Monthly Trend Chart -->
        <div class="col-md-6 mb-4">
          <div class="modern-card h-100">
            <div class="modern-card-header">
              <h5 class="mb-0">Aylık Gider Trendi</h5>
            </div>
            <div class="modern-card-body">
              <div class="chart-container" style="position: relative; height: 300px;">
                <canvas id="monthlyTrendChart"></canvas>
                 <div *ngIf="!isLoading &amp;&amp; filteredExpenses.length === 0 &amp;&amp; trendChart" class="chart-empty-state">
                  Veri Yok
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content: Table and Filters -->
    <div class="col-md-12">
      <div class="modern-card">
        <!-- Card Header: Title, Filters, Add Button -->
        <div class="modern-card-header">
          <h5 class="mb-0">Gider Kayıtları</h5>
          <div class="d-flex gap-3 align-items-center flex-wrap">
             <!-- Search Filter (member.component stili) -->
             <div class="search-input-container"> <!-- Kapsayıcı ve genişlik stili kaldırıldı -->
               <i class="fas fa-search search-icon"></i> <!-- İkon -->
               <input
                 type="text"
                 class="search-input modern-form-control modern-form-control-sm"
                 placeholder="Tür ara..."
                 [formControl]="searchControl" /> <!-- Self-closing '/>' eklendi -->
               <!-- Yorum satırı kaldırıldı (veya boş bırakıldı) -->
             </div>
            <!-- Year Filter -->
            <div class="d-flex align-items-center" style="gap: 8px;">
              <span class="filter-icon">
                <fa-icon [icon]="faCalendarAlt"></fa-icon>
              </span>
              <select id="yearFilter" class="modern-form-control modern-form-control-sm" [(ngModel)]="selectedYear" (ngModelChange)="onFilterChange()" style="min-width: 100px;">
                <option *ngFor="let year of years" [value]="year">{{ year }}</option>
              </select>
            </div>
            <!-- Month Filter -->
            <div class="d-flex align-items-center" style="gap: 8px;">
              <span class="filter-icon">
                 <fa-icon [icon]="faCalendarAlt"></fa-icon>
              </span>
              <select id="monthFilter" class="modern-form-control modern-form-control-sm" [(ngModel)]="selectedMonth" (ngModelChange)="onFilterChange()" style="min-width: 120px;">
                <option *ngFor="let month of months" [value]="month.value">{{ month.name }}</option>
              </select>
            </div>
             <!-- Clear Filters Button -->
             <button class="modern-btn modern-btn-secondary modern-btn-sm" (click)="clearFilters()" [disabled]="!hasActiveFilters()">
              <i class="fas fa-filter-circle-xmark me-1"></i> Temizle
            </button>
            <!-- Add Expense Button -->
            <button class="modern-btn modern-btn-primary modern-btn-sm" (click)="openExpenseDialog()">
              <fa-icon [icon]="faPlus" class="modern-btn-icon"></fa-icon> Yeni Gider Ekle
            </button>
          </div>
        </div>

        <!-- Card Body: Table -->
        <div class="modern-card-body">
          <!-- Active Filters -->
          <div class="active-filters mb-3" *ngIf="hasActiveFilters()">
            <span class="me-2 fw-bold">Aktif Filtreler:</span>
            <div class="d-inline-flex flex-wrap gap-2">
              <div class="filter-badge" *ngIf="searchControl.value">
                <span>Arama: {{ searchControl.value }}</span>
                <button type="button" class="btn-close ms-2" aria-label="Close" (click)="clearSearch()"></button>
              </div>
              <div class="filter-badge">
                <span>Yıl: {{ selectedYear }}</span>
                 <!-- Yıl/Ay temizleme butonu eklenmedi, Temizle butonu genel olarak var -->
              </div>
              <div class="filter-badge">
                <span>Ay: {{ getMonthName(selectedMonth) }}</span>
                 <!-- Yıl/Ay temizleme butonu eklenmedi, Temizle butonu genel olarak var -->
              </div>
            </div>
          </div>

          <!-- Expense Table -->
          <div class="table-responsive" *ngIf="!isLoading &amp;&amp; filteredExpenses.length > 0">
            <table class="modern-table">
              <thead>
                <tr>
                  <th class="text-center">Tür</th> <!-- Ortalı -->
                  <th class="text-center">Tutar</th> <!-- Ortalı -->
                  <th class="text-center">Gider Tarihi</th> <!-- Ortalı -->
                  <th class="text-center">Açıklama</th> <!-- Ortalı -->
                  <th class="text-center">İşlemler</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let expense of filteredExpenses; let i = index" [style.animation-delay]="i * 0.05 + 's'" class="staggered-item">
                  <!-- Tür Sütunu (Ortalı - text-center eklendi) -->
                  <td class="text-center">
                     <span class="modern-badge" [ngClass]="getBadgeClass(expense.expenseType)" *ngIf="expense.expenseType">
                       {{ expense.expenseType }}
                     </span>
                     <span *ngIf="!expense.expenseType">-</span>
                   </td>
                  <td class="text-center" style="font-weight: 700; color: var(--danger);">{{ expense.amount | currency:'₺':'symbol':'1.2-2':'tr' }}</td> <!-- Ortalı -->
                  <td class="text-center">{{ expense.expenseDate | date:'dd/MM/yyyy' }}</td> <!-- Ortalı -->
                  <!-- Açıklama Sütunu (Ortalı - text-center eklendi) -->
                  <td class="text-center">{{ expense.description }}</td>
                  <td>
                    <div class="d-flex justify-content-center gap-2">
                      <!-- Edit Button -->
                      <button
                        class="modern-btn modern-btn-primary modern-btn-sm modern-btn-icon-only"
                        title="Düzenle"
                        (click)="openExpenseDialog(expense)"
                      >
                        <fa-icon [icon]="faEdit"></fa-icon>
                      </button>
                      <!-- Delete Button -->
                      <button
                        class="modern-btn modern-btn-danger modern-btn-sm modern-btn-icon-only"
                        title="Sil"
                        (click)="deleteExpense(expense)"
                      >
                        <fa-icon [icon]="faTrashAlt"></fa-icon>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- No Results Message (Empty State) -->
          <div class="empty-state" *ngIf="!isLoading &amp;&amp; filteredExpenses.length === 0">
            <i class="fas fa-ghost fa-3x text-muted mb-3"></i>
            <h5 class="mt-3">Gider Bulunamadı</h5>
            <p class="text-muted">Seçili kriterlere uygun gider kaydı bulunamadı.</p>
            <button class="modern-btn modern-btn-primary mt-3" (click)="openExpenseDialog()">
              <fa-icon [icon]="faPlus" class="modern-btn-icon"></fa-icon> İlk Gideri Ekle
            </button>
          </div>

        </div> <!-- End Card Body -->
      </div> <!-- End Modern Card -->
    </div> <!-- End Col -->
  </div> <!-- End Row -->
</div> <!-- End Container -->