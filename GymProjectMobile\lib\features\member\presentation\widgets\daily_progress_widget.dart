import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/core.dart';
import '../../../../shared/widgets/responsive_builder.dart';
import '../../domain/models/daily_workout_progress.dart';
import '../providers/exercise_progress_provider.dart';

/// Günlük antrenman ilerleme widget'ı
class DailyProgressWidget extends ConsumerWidget {
  final DateTime? date;
  final bool showDetails;

  const DailyProgressWidget({
    super.key,
    this.date,
    this.showDetails = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ResponsiveBuilder(
      builder: (context, deviceType) {
        final theme = Theme.of(context);
        final progressState = ref.watch(exerciseProgressProvider);

        if (progressState.isLoading) {
          return _buildLoadingWidget(context, theme, deviceType);
        }

        final dailyProgress = progressState.dailyProgress;
        if (dailyProgress == null) {
          return _buildNoDataWidget(context, theme, deviceType);
        }

        return _buildProgressWidget(context, theme, deviceType, dailyProgress);
      },
    );
  }

  /// Loading widget
  Widget _buildLoadingWidget(BuildContext context, ThemeData theme, DeviceType deviceType) {
    return Container(
      padding: AppSpacing.responsiveCardPadding(context),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(AppSpacing.responsiveBorderRadius(context,
          mobile: 12.0,
          tablet: 16.0,
          desktop: 20.0,
        )),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.analytics_outlined,
                color: theme.colorScheme.primary,
                size: AppSpacing.responsiveIconSize(context,
                  mobile: 24.0,
                  tablet: 28.0,
                  desktop: 32.0,
                ),
              ),
              ResponsiveSpacing.horizontal(
                mobile: 8.0,
                tablet: 10.0,
                desktop: 12.0,
              ),
              Expanded(
                child: ResponsiveText(
                  'Günlük İlerleme',
                  textType: 'h4',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          ResponsiveSpacing.vertical(
            mobile: 12.0,
            tablet: 16.0,
            desktop: 20.0,
          ),
          const Center(
            child: CircularProgressIndicator(),
          ),
        ],
      ),
    );
  }

  /// Veri yok widget
  Widget _buildNoDataWidget(BuildContext context, ThemeData theme, DeviceType deviceType) {
    return Container(
      padding: AppSpacing.responsiveCardPadding(context),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(AppSpacing.responsiveBorderRadius(context,
          mobile: 12.0,
          tablet: 16.0,
          desktop: 20.0,
        )),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.analytics_outlined,
                color: theme.colorScheme.primary,
                size: AppSpacing.responsiveIconSize(context,
                  mobile: 24.0,
                  tablet: 28.0,
                  desktop: 32.0,
                ),
              ),
              ResponsiveSpacing.horizontal(
                mobile: 8.0,
                tablet: 10.0,
                desktop: 12.0,
              ),
              Expanded(
                child: ResponsiveText(
                  'Günlük İlerleme',
                  textType: 'h4',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          ResponsiveSpacing.vertical(
            mobile: 12.0,
            tablet: 16.0,
            desktop: 20.0,
          ),
          ResponsiveText(
            'Henüz antrenman verisi bulunmuyor',
            textType: 'bodymedium',
            style: TextStyle(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
        ],
      ),
    );
  }

  /// İlerleme widget
  Widget _buildProgressWidget(BuildContext context, ThemeData theme, DeviceType deviceType, DailyWorkoutProgress progress) {
    return Container(
      padding: AppSpacing.responsiveCardPadding(context),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(AppSpacing.responsiveBorderRadius(context,
          mobile: 12.0,
          tablet: 16.0,
          desktop: 20.0,
        )),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Başlık
          Row(
            children: [
              Text(
                progress.progressCategory.emoji,
                style: TextStyle(
                  fontSize: AppSpacing.responsiveIconSize(context,
                    mobile: 24.0,
                    tablet: 28.0,
                    desktop: 32.0,
                  ),
                ),
              ),
              ResponsiveSpacing.horizontal(
                mobile: 8.0,
                tablet: 10.0,
                desktop: 12.0,
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ResponsiveText(
                      'Günlük İlerleme',
                      textType: 'h4',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    ResponsiveText(
                      progress.progressCategory.displayName,
                      textType: 'bodysmall',
                      style: TextStyle(
                        color: _getProgressColor(theme, progress.progressCategory),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: AppSpacing.responsive(null,
                    mobile: 8.0,
                    tablet: 10.0,
                    desktop: 12.0,
                  ),
                  vertical: AppSpacing.responsive(null,
                    mobile: 4.0,
                    tablet: 6.0,
                    desktop: 8.0,
                  ),
                ),
                decoration: BoxDecoration(
                  color: _getProgressColor(theme, progress.progressCategory).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(AppSpacing.responsiveBorderRadius(null,
                    mobile: 8.0,
                    tablet: 10.0,
                    desktop: 12.0,
                  )),
                ),
                child: ResponsiveText(
                  '${progress.completionPercentage.toStringAsFixed(0)}%',
                  textType: 'bodymedium',
                  style: TextStyle(
                    color: _getProgressColor(theme, progress.progressCategory),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),

          ResponsiveSpacing.vertical(
            mobile: 12.0,
            tablet: 16.0,
            desktop: 20.0,
          ),

          // Progress bar
          ClipRRect(
            borderRadius: BorderRadius.circular(AppSpacing.responsiveBorderRadius(null,
              mobile: 4.0,
              tablet: 6.0,
              desktop: 8.0,
            )),
            child: LinearProgressIndicator(
              value: progress.completionPercentage / 100,
              backgroundColor: theme.colorScheme.outline.withValues(alpha: 0.2),
              valueColor: AlwaysStoppedAnimation<Color>(
                _getProgressColor(theme, progress.progressCategory),
              ),
              minHeight: AppSpacing.responsive(null,
                mobile: 8.0,
                tablet: 10.0,
                desktop: 12.0,
              ),
            ),
          ),

          if (showDetails) ...[
            ResponsiveSpacing.vertical(
              mobile: 12.0,
              tablet: 16.0,
              desktop: 20.0,
            ),

            // Detaylar
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    theme,
                    deviceType,
                    Icons.fitness_center,
                    'Egzersiz',
                    '${progress.completedExercises}/${progress.totalExercises}',
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    theme,
                    deviceType,
                    Icons.repeat,
                    'Set',
                    '${progress.completedSets}/${progress.totalSets}',
                  ),
                ),
                if (progress.remainingExercises > 0)
                  Expanded(
                    child: _buildStatItem(
                      theme,
                      deviceType,
                      Icons.schedule,
                      'Kalan',
                      '${progress.remainingExercises}',
                    ),
                  ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  /// İstatistik item'ı
  Widget _buildStatItem(ThemeData theme, DeviceType deviceType, IconData icon, String label, String value) {
    return Column(
      children: [
        Icon(
          icon,
          size: AppSpacing.responsiveIconSize(null,
            mobile: 18.0,
            tablet: 20.0,
            desktop: 22.0,
          ),
          color: theme.colorScheme.primary,
        ),
        ResponsiveSpacing.vertical(
          mobile: 4.0,
          tablet: 6.0,
          desktop: 8.0,
        ),
        ResponsiveText(
          value,
          textType: 'bodymedium',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        ResponsiveText(
          label,
          textType: 'bodysmall',
          style: TextStyle(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
          ),
        ),
      ],
    );
  }

  /// İlerleme kategorisine göre renk
  Color _getProgressColor(ThemeData theme, ProgressCategory category) {
    switch (category) {
      case ProgressCategory.none:
        return theme.colorScheme.outline;
      case ProgressCategory.poor:
        return Colors.red;
      case ProgressCategory.average:
        return Colors.orange;
      case ProgressCategory.good:
        return Colors.blue;
      case ProgressCategory.excellent:
        return Colors.green;
    }
  }
}
