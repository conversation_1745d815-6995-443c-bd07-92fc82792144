using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;

namespace Business.Abstract
{
    /// <summary>
    /// Üye egzersiz ilerleme takip service interface
    /// Performance optimized for 10.000+ users
    /// </summary>
    public interface IMemberExerciseProgressService
    {
        /// <summary>
        /// Egzersiz tamamlama kaydı ekler
        /// </summary>
        IResult Add(MemberExerciseProgressAddDto progressAddDto);

        /// <summary>
        /// Egzersiz tamamlama kaydını günceller
        /// </summary>
        IResult Update(MemberExerciseProgressUpdateDto progressUpdateDto);

        /// <summary>
        /// Egzersiz tamamlama kaydını siler (soft delete)
        /// </summary>
        IResult Delete(int memberExerciseProgressId);

        /// <summary>
        /// ID'ye göre egzersiz tamamlama kaydını getirir
        /// </summary>
        IDataResult<MemberExerciseProgress> GetById(int memberExerciseProgressId);

        /// <summary>
        /// Şirket bazlı tüm egzersiz ilerlemelerini getirir (admin paneli için)
        /// </summary>
        IDataResult<List<MemberExerciseProgressListDto>> GetCompanyProgressList(int page = 1, int pageSize = 50);

        /// <summary>
        /// Belirli üyenin egzersiz ilerlemelerini getirir
        /// </summary>
        IDataResult<List<MemberExerciseProgressDto>> GetMemberProgressList(int memberId, int page = 1, int pageSize = 50);

        /// <summary>
        /// Belirli üyenin belirli tarihteki ilerlemelerini getirir
        /// </summary>
        IDataResult<List<MemberExerciseProgressDto>> GetMemberProgressByDate(int memberId, DateTime date);

        /// <summary>
        /// Üyenin program bazlı ilerlemelerini getirir
        /// </summary>
        IDataResult<List<MemberExerciseProgressDto>> GetMemberProgressByProgram(int memberId, int memberWorkoutProgramId);

        /// <summary>
        /// Üyenin egzersiz istatistiklerini getirir
        /// </summary>
        IDataResult<MemberExerciseProgressStatsDto> GetMemberProgressStats(int memberId);

        /// <summary>
        /// Belirli egzersizin tamamlanma durumunu kontrol eder
        /// </summary>
        IDataResult<ExerciseCompletionStatusDto> GetExerciseCompletionStatus(int memberId, int workoutProgramExerciseId, DateTime date);

        /// <summary>
        /// Üyenin günlük antrenman ilerlemesini getirir
        /// </summary>
        IDataResult<DailyWorkoutProgressDto> GetDailyWorkoutProgress(int memberId, DateTime date);

        /// <summary>
        /// Üyenin haftalık antrenman ilerlemesini getirir
        /// </summary>
        IDataResult<List<DailyWorkoutProgressDto>> GetWeeklyWorkoutProgress(int memberId, DateTime startDate);

        /// <summary>
        /// Üyenin aylık antrenman ilerlemesini getirir
        /// </summary>
        IDataResult<List<DailyWorkoutProgressDto>> GetMonthlyWorkoutProgress(int memberId, int year, int month);

        /// <summary>
        /// Belirli program için tüm egzersizlerin tamamlanma durumunu getirir (mobil API için)
        /// </summary>
        IDataResult<List<ExerciseCompletionStatusDto>> GetProgramExerciseCompletionStatus(int memberId, int memberWorkoutProgramId, DateTime date);

        /// <summary>
        /// Şirket bazlı egzersiz tamamlama istatistiklerini getirir
        /// </summary>
        IDataResult<List<MemberExerciseProgressStatsDto>> GetCompanyProgressStats(DateTime startDate, DateTime endDate);

        /// <summary>
        /// En aktif üyeleri getirir (leaderboard için)
        /// </summary>
        IDataResult<List<MemberExerciseProgressStatsDto>> GetTopActiveMembers(int topCount = 10, DateTime? startDate = null);

        /// <summary>
        /// Belirli tarih aralığındaki toplam tamamlanan egzersiz sayısını getirir
        /// </summary>
        IDataResult<int> GetTotalCompletedExercisesCount(DateTime startDate, DateTime endDate);

        /// <summary>
        /// Mobil API için - User ID'ye göre günlük egzersiz tamamlama durumunu getirir
        /// </summary>
        IDataResult<List<ExerciseCompletionStatusDto>> GetExerciseCompletionStatusByUserId(int userId, DateTime date);

        /// <summary>
        /// Mobil API için - User ID'ye göre günlük antrenman ilerlemesini getirir
        /// </summary>
        IDataResult<DailyWorkoutProgressDto> GetDailyWorkoutProgressByUserId(int userId, DateTime date);

        /// <summary>
        /// Mobil API için - User ID'ye göre egzersiz tamamlama kaydı ekler
        /// </summary>
        IResult AddProgressByUserId(int userId, MemberExerciseProgressAddDto progressAddDto);
    }
}
