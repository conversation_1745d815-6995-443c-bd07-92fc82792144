// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserModel _$UserModelFromJson(Map<String, dynamic> json) => UserModel(
  nameidentifier: json['nameidentifier'] as String,
  email: json['email'] as String,
  name: json['name'] as String,
  role: json['role'] as String,
  companyId: (json['companyId'] as num).toInt(),
  nbf: (json['nbf'] as num).toInt(),
  exp: (json['exp'] as num).toInt(),
  iss: json['iss'] as String,
  aud: json['aud'] as String,
);

Map<String, dynamic> _$UserModelToJson(UserModel instance) => <String, dynamic>{
  'nameidentifier': instance.nameidentifier,
  'email': instance.email,
  'name': instance.name,
  'role': instance.role,
  'companyId': instance.companyId,
  'nbf': instance.nbf,
  'exp': instance.exp,
  'iss': instance.iss,
  'aud': instance.aud,
};

DeviceInfo _$DeviceInfoFromJson(Map<String, dynamic> json) => DeviceInfo(
  deviceType: json['deviceType'] as String,
  deviceId: json['deviceId'] as String,
  deviceName: json['deviceName'] as String,
  osVersion: json['osVersion'] as String,
  appVersion: json['appVersion'] as String,
);

Map<String, dynamic> _$DeviceInfoToJson(DeviceInfo instance) =>
    <String, dynamic>{
      'deviceType': instance.deviceType,
      'deviceId': instance.deviceId,
      'deviceName': instance.deviceName,
      'osVersion': instance.osVersion,
      'appVersion': instance.appVersion,
    };
