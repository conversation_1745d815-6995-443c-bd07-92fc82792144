import { Component, OnInit, Input, Output, EventEmitter, OnDestroy } from '@angular/core';
import { AuthService } from '../../services/auth.service';
import { Router } from '@angular/router';
import { MemberService } from '../../services/member.service';
import { FileUploadService } from '../../services/file-upload.service';
import { ProfileImageUpdateService } from '../../services/profile-image-update.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-sidebar',
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.css'],
  standalone: false
})
export class SidebarComponent implements OnInit, OnDestroy {
@Input() collapsed: boolean = false;
@Input() isDarkMode: boolean = false;
@Output() toggleSidebar = new EventEmitter<void>();
@Output() toggleDarkMode = new EventEmitter<void>();

  isOwner: boolean = false;
  isAdmin: boolean = false;
  isMember: boolean = false;
  hasMemberQRAccess: boolean = false;

  // Profile image properties
  profileImageUrl: string | null = null;
  hasProfileImage: boolean = false;

  // Menu sections
  customerMenuOpen: boolean = false;
  eMoneyMenuOpen: boolean = false;
  trainingMenuOpen: boolean = false;
  licenseMenuOpen: boolean = false;
  gymMenuOpen: boolean = false;

  // Subscriptions
  private profileImageUpdateSubscription?: Subscription;

  constructor(
    private authService: AuthService,
    private router: Router,
    private memberService: MemberService,
    private fileUploadService: FileUploadService,
    private profileImageUpdateService: ProfileImageUpdateService
  ) {
    // Subscribe to user changes
    this.authService.currentUser.subscribe(user => {
      if (user) {
        this.isOwner = this.authService.hasRole('owner');
        this.isAdmin = this.authService.hasRole('admin');
        this.isMember = this.authService.hasRole('member');

        // Load profile image
        this.loadProfileImage();

        // Eğer member rolüne sahipse, QR kodu erişimi kontrolü yap
        if (this.isMember) {
          this.checkMemberQRAccess();
        } else {
          this.hasMemberQRAccess = false;
        }
      } else {
        this.isOwner = false;
        this.isAdmin = false;
        this.isMember = false;
        this.hasMemberQRAccess = false;
        this.profileImageUrl = null;
        this.hasProfileImage = false;
      }
    });
  }

  ngOnInit() {
    // Initial check
    this.isOwner = this.authService.hasRole('owner');
    this.isAdmin = this.authService.hasRole('admin');
    this.isMember = this.authService.hasRole('member');

    // Load profile image
    this.loadProfileImage();

    // Eğer member rolüne sahipse, QR kodu erişimi kontrolü yap
    if (this.isMember) {
      this.checkMemberQRAccess();
    }

    // Subscribe to profile image updates
    this.profileImageUpdateSubscription = this.profileImageUpdateService.profileImageUpdated$.subscribe(() => {
      this.loadProfileImage();
    });
  }

  ngOnDestroy() {
    if (this.profileImageUpdateSubscription) {
      this.profileImageUpdateSubscription.unsubscribe();
    }
  }

  // Üyenin QR koduna erişim hakkı olup olmadığını kontrol et
  checkMemberQRAccess() {
    if (!this.isMember) return;

    // Kullanıcı member rolüne sahipse, QR butonunu göster
    // API çağrısı yapmadan direkt olarak erişim veriyoruz
    // Gerçek kontrol my-qr component içinde yapılacak
    this.hasMemberQRAccess = true;
  }

  // Toggle sidebar
  onToggleSidebar() {
    this.toggleSidebar.emit();
  }

  // Toggle dark mode
  onToggleDarkMode() {
    this.toggleDarkMode.emit();
  }

  // Toggle menu sections
  toggleMenuSection(section: string) {
    if (!this.collapsed) {
      switch(section) {
        case 'customer':
          this.customerMenuOpen = !this.customerMenuOpen;
          break;
        case 'eMoney':
          this.eMoneyMenuOpen = !this.eMoneyMenuOpen;
          break;
        case 'training':
          this.trainingMenuOpen = !this.trainingMenuOpen;
          break;
        case 'license':
          this.licenseMenuOpen = !this.licenseMenuOpen;
          break;
        case 'gym':
          this.gymMenuOpen = !this.gymMenuOpen;
          break;
      }
    }
  }

  logout(event: Event) {
    event.preventDefault();
    this.authService.logout();
  }

  isAuthenticated(): boolean {
    return this.authService.isAuthenticated();
  }

  // Profile image management
  loadProfileImage() {
    const currentUser = this.authService.currentUserValue;
    if (currentUser?.nameidentifier) {
      const userId = parseInt(currentUser.nameidentifier);
      // Önce profil fotoğrafı URL'ini oluştur (timestamp olmadan)
      const baseUrl = this.fileUploadService.getProfileImageUrl(userId);
      // Cache bypass için timestamp ekle
      const timestamp = new Date().getTime();
      this.profileImageUrl = `${baseUrl}?t=${timestamp}`;
      // Başlangıçta true olarak ayarla, error durumunda false yapılacak
      this.hasProfileImage = true;
    } else {
      this.profileImageUrl = null;
      this.hasProfileImage = false;
    }
  }

  // Profile image error handling
  onProfileImageError() {
    this.hasProfileImage = false;
    this.profileImageUrl = null;
  }

  // Profile image load success
  onProfileImageLoad() {
    this.hasProfileImage = true;
  }
}
