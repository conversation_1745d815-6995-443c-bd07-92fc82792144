import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { LicensePackageService } from '../../services/license-package.service';
import { UserLicenseService } from '../../services/user-license.service';
import { LicensePackage } from '../../models/licensePackage';
import { LicensePurchaseDto } from '../../models/LicensePurchaseDto';
import { User } from '../../models/user';
import { UserService } from '../../services/user-service.service';

@Component({
  selector: 'app-license-purchase',
  templateUrl: './license-purchase.component.html',
  styleUrls: ['./license-purchase.component.css'],
  standalone:false
})
export class LicensePurchaseComponent implements OnInit {
  purchaseForm: FormGroup;
  licensePackages: LicensePackage[] = [];
  users: User[] = [];
  isLoading = false;
  isSubmitting = false;
  paymentMethods: string[] = ['Nakit', '<PERSON><PERSON><PERSON>', 'Havale/EFT'];
  filteredUsers: User[] = [];
  filteredPackages: LicensePackage[] = [];
  
  constructor(
    private fb: FormBuilder,
    private licensePackageService: LicensePackageService,
    private userLicenseService: UserLicenseService,
    private userService: UserService,
    private toastr: ToastrService,
    public dialogRef: MatDialogRef<LicensePurchaseComponent>
  ) {
    this.purchaseForm = this.fb.group({
      userID: [null, Validators.required],
      licensePackageID: [null, Validators.required],
      paymentMethod: ['', Validators.required]
    });
  }

  ngOnInit(): void {
    this.loadLicensePackages();
    this.loadUsers();

    // Filter users as the user types
    this.purchaseForm.get('userID')?.valueChanges.subscribe(value => {
      if (typeof value === 'string') {
        this.filterUsers(value);
      }
    });

    // Filter packages as the user types
    this.purchaseForm.get('licensePackageID')?.valueChanges.subscribe(value => {
      if (typeof value === 'string') {
        this.filterPackages(value);
      }
    });
  }

  loadLicensePackages(): void {
    this.isLoading = true;
    this.licensePackageService.getAll().subscribe({
      next: (response) => {
        this.licensePackages = response.data;
        this.filteredPackages = this.licensePackages;
        this.isLoading = false;
      },
      error: (error) => {
        this.toastr.error('Lisans paketleri yüklenirken bir hata oluştu', 'Hata');
        this.isLoading = false;
      }
    });
  }

  loadUsers(): void {
    this.isLoading = true;
    this.userService.getAll().subscribe({
      next: (response) => {
        this.users = response.data;
        this.filteredUsers = this.users;
        this.isLoading = false;
      },
      error: (error) => {
        this.toastr.error('Kullanıcılar yüklenirken bir hata oluştu', 'Hata');
        this.isLoading = false;
      }
    });
  }

  filterUsers(value: string): void {
    const filterValue = value.toLowerCase();
    this.filteredUsers = this.users.filter(user => 
      user.firstName.toLowerCase().includes(filterValue) ||
      user.lastName.toLowerCase().includes(filterValue) ||
      user.email.toLowerCase().includes(filterValue)
    );
  }

  filterPackages(value: string): void {
    const filterValue = value.toLowerCase();
    this.filteredPackages = this.licensePackages.filter(pkg => 
      pkg.name.toLowerCase().includes(filterValue) ||
      pkg.description.toLowerCase().includes(filterValue) ||
      pkg.role.toLowerCase().includes(filterValue)
    );
  }

  displayUser(user: User): string {
    return user ? `${user.firstName} ${user.lastName} (${user.email})` : '';
  }

  displayPackage(pkg: LicensePackage): string {
    return pkg ? `${pkg.name} - ${pkg.role} (${pkg.durationDays} gün)` : '';
  }

  onSubmit(): void {
    if (this.purchaseForm.invalid) {
      this.toastr.error('Lütfen formu doğru şekilde doldurun', 'Hata');
      return;
    }

    this.isSubmitting = true;
    
    const formValue = this.purchaseForm.value;
    const licensePurchaseDto: LicensePurchaseDto = {
      userID: typeof formValue.userID === 'object' ? formValue.userID.userID : formValue.userID,
      licensePackageID: typeof formValue.licensePackageID === 'object' ? formValue.licensePackageID.licensePackageID : formValue.licensePackageID,
      paymentMethod: formValue.paymentMethod
    };

    this.userLicenseService.purchase(licensePurchaseDto).subscribe({
      next: (response) => {
        this.toastr.success(response.message, 'Başarılı');
        this.dialogRef.close(true);
        this.isSubmitting = false;
      },
      error: (error) => {
        this.toastr.error('Lisans satın alınırken bir hata oluştu', 'Hata');
        this.isSubmitting = false;
      }
    });
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }
}
