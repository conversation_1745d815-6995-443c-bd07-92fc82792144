import { Component, OnInit } from '@angular/core';
import { MemberRemainingDayService } from '../../services/member-remaining-day.service';
import { memberRemainingDay as MemberRemainingDay } from '../../models/memberRemainingDay';
import { ToastrService } from 'ngx-toastr';

// Sıralama yönü için enum
enum SortDirection {
  ASC = 'asc',
  DESC = 'desc'
}

@Component({
  selector: 'app-member-remaining-day',
  templateUrl: './member-remaining-day.component.html',
  styleUrls: ['./member-remaining-day.component.css'],
  standalone: false
})
export class MemberRemainingDayComponent implements OnInit {
  memberRemainingDays: MemberRemainingDay[] = [];
  filteredMembers: MemberRemainingDay[] = [];
  searchText: string = '';
  isLoading: boolean = false;
  
  // Sıralama durumu için değişkenler
  sortDirection: SortDirection = SortDirection.ASC; // Varsayılan olarak artan sıralama
  sortColumn: string = 'remainingDays'; // Varsayılan olarak kalan günlere göre sıralama

  constructor(
    private memberRemainingDayService: MemberRemainingDayService,
    private toastrService: ToastrService
  ) { }

  ngOnInit(): void {
    this.getMemberRemainingDays();
  }

  getMemberRemainingDays() {
    this.isLoading = true;
    this.memberRemainingDayService.getMemberRemainingDays().subscribe(
      (response) => {
        this.memberRemainingDays = response.data;
        this.filterMembers(); // Bu metod içinde sıralama da yapılacak
        this.isLoading = false;
      },
      (error) => {
        this.toastrService.error('Üyelik süresi yaklaşan üyeler yüklenirken bir hata oluştu.', 'Hata');
        this.isLoading = false;
      }
    );
  }
  
  // Sıralama yönünü değiştir
  toggleSort() {
    this.sortDirection = this.sortDirection === SortDirection.ASC 
      ? SortDirection.DESC 
      : SortDirection.ASC;
    
    this.sortMembers();
  }
  
  // Üyeleri kalan günlere göre sırala
  sortMembers() {
    this.filteredMembers.sort((a, b) => {
      if (this.sortDirection === SortDirection.ASC) {
        return a.remainingDays - b.remainingDays; // Artan sıralama (en az kalan gün üstte)
      } else {
        return b.remainingDays - a.remainingDays; // Azalan sıralama (en çok kalan gün üstte)
      }
    });
  }
  
  // Filter members based on search text
  filterMembers() {
    if (!this.searchText) {
      this.filteredMembers = [...this.memberRemainingDays];
    } else {
      const searchLower = this.searchText.toLowerCase();
      this.filteredMembers = this.memberRemainingDays.filter(member => 
        member.memberName.toLowerCase().includes(searchLower) || 
        member.phoneNumber.toLowerCase().includes(searchLower) ||
        member.branch.toLowerCase().includes(searchLower)
      );
    }
    
    // Filtreleme sonrası sıralamayı uygula
    this.sortMembers();
  }
  
  // Get total count of members with expiring memberships
  getTotalExpiringCount(): number {
    return this.memberRemainingDays.length;
  }
  
  // Get count of members with urgent expiring memberships (less than 3 days)
  getUrgentExpiringCount(): number {
    return this.memberRemainingDays.filter(member => member.remainingDays <= 3).length;
  }
  
  // Get average remaining days
  getAverageRemainingDays(): string {
    if (this.memberRemainingDays.length === 0) return '0';
    
    const totalDays = this.memberRemainingDays.reduce((sum, member) => sum + member.remainingDays, 0);
    const average = totalDays / this.memberRemainingDays.length;
    
    return average.toFixed(1);
  }
  
  // Get CSS class for remaining days text
  getRemainingDaysClass(days: number): string {
    if (days <= 3) return 'text-danger fw-bold';
    if (days <= 5) return 'text-warning fw-bold';
    return 'text-success';
  }
  
  // Get status text based on remaining days
  getStatusText(days: number): string {
    if (days <= 3) return 'Acil';
    if (days <= 5) return 'Yakında Bitiyor';
    return 'Normal';
  }
  
  // Get status badge class based on remaining days
  getStatusClass(days: number): string {
    if (days <= 3) return 'status-danger';
    if (days <= 5) return 'status-warning';
    return 'status-success';
  }
  
  // Get initials from name
  getInitials(name: string): string {
    if (!name) return '';
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  }
  
  // Generate avatar background color based on name
  getAvatarColor(name: string): string {
    if (!name) return '#ffc107';
    
    const colors = [
      '#ffc107', '#fd7e14', '#20c997', '#0dcaf0', 
      '#0d6efd', '#6610f2', '#6f42c1', '#d63384'
    ];
    
    // Simple hash function to get consistent color for the same name
    let hash = 0;
    for (let i = 0; i < name.length; i++) {
      hash = name.charCodeAt(i) + ((hash << 5) - hash);
    }
    
    return colors[Math.abs(hash) % colors.length];
  }
  
  // Open WhatsApp with the member's phone number
  openWhatsApp(phoneNumber: string): void {
    if (!phoneNumber) {
      this.toastrService.error('Telefon numarası bulunamadı.', 'Hata');
      return;
    }
    
    // Format the phone number (remove any non-digit characters)
    const formattedNumber = phoneNumber.replace(/\D/g, '');
    
    // Check if the number starts with '0', if so remove it
    const whatsappNumber = formattedNumber.startsWith('0') 
      ? formattedNumber.substring(1) 
      : formattedNumber;
    
    // Create WhatsApp URL with country code (assuming Turkey +90)
    const whatsappUrl = `https://wa.me/90${whatsappNumber}`;
    
    // Open WhatsApp in a new tab
    window.open(whatsappUrl, '_blank');
  }
}
