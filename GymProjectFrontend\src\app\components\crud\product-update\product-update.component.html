<div class="modern-dialog fade-in">
  <div class="modern-dialog-header">
    <h2 class="modern-dialog-title"><PERSON><PERSON><PERSON><PERSON></h2>
    <button class="modern-btn modern-btn-sm" (click)="onNoClick()">
      <i class="fas fa-times"></i>
    </button>
  </div>

  <div class="modern-dialog-content">
    <form [formGroup]="updateForm" (ngSubmit)="onSubmit()">
      <div class="modern-form-group">
        <label for="name" class="modern-form-label"><PERSON><PERSON><PERSON><PERSON></label>
        <div class="d-flex align-items-center">
          <div class="input-group-text me-2">
            <i class="fas fa-box"></i>
          </div>
          <input type="text" class="modern-form-control" id="name" formControlName="name" placeholder="Ürün adı" style="width: 95%;">
        </div>
        <small class="text-danger" *ngIf="updateForm.get('name')?.invalid && updateForm.get('name')?.touched">
          Ür<PERSON>n adı gereklidir
        </small>
      </div>

      <div class="modern-form-group">
        <label for="price" class="modern-form-label">Fiyat</label>
        <div class="d-flex align-items-center">
          <div class="input-group-text me-2">
            <i class="fas fa-money-bill-wave"></i>
          </div>
          <input type="number" class="modern-form-control" id="price" formControlName="price" placeholder="0.00" style="width: 95%;">
        </div>
        <small class="text-danger" *ngIf="updateForm.get('price')?.invalid && updateForm.get('price')?.touched">
          Geçerli bir fiyat giriniz
        </small>
      </div>

      <div class="d-flex justify-content-between mt-4">
        <button type="button" class="modern-btn modern-btn-secondary" (click)="onNoClick()">
          <i class="fas fa-times modern-btn-icon"></i> İptal
        </button>
        <button type="submit" class="modern-btn modern-btn-primary" [disabled]="!updateForm.valid">
          <i class="fas fa-save modern-btn-icon"></i> Güncelle
        </button>
      </div>
    </form>
  </div>
</div>

<style>
  .modern-dialog {
    max-width: 450px;
    width: 100%;
    background-color: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
  }

  .modern-dialog-header {
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--bg-secondary);
  }

  .modern-dialog-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
  }

  .modern-dialog-content {
    padding: var(--spacing-lg);
  }
</style>
