/// Egzersiz tamamlanma durumu modeli
class ExerciseCompletionStatus {
  final int workoutProgramExerciseId;
  final bool isCompleted;
  final DateTime? completedDate;
  final int? completedSets;
  final String? actualReps;
  final String? notes;

  ExerciseCompletionStatus({
    required this.workoutProgramExerciseId,
    required this.isCompleted,
    this.completedDate,
    this.completedSets,
    this.actualReps,
    this.notes,
  });

  factory ExerciseCompletionStatus.fromJson(Map<String, dynamic> json) {
    return ExerciseCompletionStatus(
      workoutProgramExerciseId: json['workoutProgramExerciseID'] ?? 0,
      isCompleted: json['isCompleted'] ?? false,
      completedDate: json['completedDate'] != null 
          ? DateTime.parse(json['completedDate'])
          : null,
      completedSets: json['completedSets'],
      actualReps: json['actualReps'],
      notes: json['notes'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'workoutProgramExerciseID': workoutProgramExerciseId,
      'isCompleted': isCompleted,
      'completedDate': completedDate?.toIso8601String(),
      'completedSets': completedSets,
      'actualReps': actualReps,
      'notes': notes,
    };
  }

  ExerciseCompletionStatus copyWith({
    int? workoutProgramExerciseId,
    bool? isCompleted,
    DateTime? completedDate,
    int? completedSets,
    String? actualReps,
    String? notes,
  }) {
    return ExerciseCompletionStatus(
      workoutProgramExerciseId: workoutProgramExerciseId ?? this.workoutProgramExerciseId,
      isCompleted: isCompleted ?? this.isCompleted,
      completedDate: completedDate ?? this.completedDate,
      completedSets: completedSets ?? this.completedSets,
      actualReps: actualReps ?? this.actualReps,
      notes: notes ?? this.notes,
    );
  }

  @override
  String toString() {
    return 'ExerciseCompletionStatus(workoutProgramExerciseId: $workoutProgramExerciseId, isCompleted: $isCompleted, completedDate: $completedDate, completedSets: $completedSets, actualReps: $actualReps, notes: $notes)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is ExerciseCompletionStatus &&
        other.workoutProgramExerciseId == workoutProgramExerciseId &&
        other.isCompleted == isCompleted &&
        other.completedDate == completedDate &&
        other.completedSets == completedSets &&
        other.actualReps == actualReps &&
        other.notes == notes;
  }

  @override
  int get hashCode {
    return workoutProgramExerciseId.hashCode ^
        isCompleted.hashCode ^
        completedDate.hashCode ^
        completedSets.hashCode ^
        actualReps.hashCode ^
        notes.hashCode;
  }
}
