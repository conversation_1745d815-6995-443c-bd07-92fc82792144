:root {
  --primary-color: #4361ee;
  --secondary-color: #3a0ca3;
  --accent-color: #f72585;
  --success-color: #4cc9f0;
  --warning-color: #f8961e;
  --danger-color: #f94144;
  --text-color: #2b2d42;
  --text-muted: #6c757d;
  --background-color: #f8f9fa;
  --card-bg-color: #ffffff;
  --input-bg: #f8f9fa;
  --input-border: #ced4da;
  --input-text: #495057;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --border-radius: 12px;
}

.login-container {
  height: 100vh;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--background-color);
  background-image: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
}

.login-wrapper {
  display: flex;
  width: 90%;
  max-width: 1200px;
  height: 650px;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: 0 20px 40px var(--shadow-color);
  position: relative;
}

/* Left Panel - Image */
.login-image-panel {
  flex: 1.2;
  background-image: url('https://images.unsplash.com/photo-1571902943202-507ec2618e8f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80');
  background-size: cover;
  background-position: center;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(67, 97, 238, 0.85) 0%, rgba(58, 12, 163, 0.85) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(2px);
}

.gym-branding {
  text-align: center;
  color: white;
  padding: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  max-width: 500px;
}

.logo-container {
  width: 100px;
  height: 100px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.gym-branding i {
  font-size: 50px;
  color: white;
}

.gym-branding h1 {
  font-size: 42px;
  font-weight: 700;
  margin-bottom: 5px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.gym-branding p {
  font-size: 18px;
  opacity: 0.9;
  margin-bottom: 30px;
}

.features {
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: 100%;
  max-width: 300px;
}

.feature {
  display: flex;
  align-items: center;
  gap: 15px;
  background: rgba(255, 255, 255, 0.1);
  padding: 12px 20px;
  border-radius: 10px;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.feature:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-3px);
}

.feature i {
  font-size: 20px;
  color: white;
}

.feature span {
  font-size: 16px;
  font-weight: 500;
}

/* Right Panel - Form */
.login-form-panel {
  flex: 0.8;
  background-color: var(--card-bg-color);
  padding: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.login-form-panel::before {
  content: '';
  position: absolute;
  top: -50px;
  right: -50px;
  width: 100px;
  height: 100px;
  background: var(--primary-color);
  opacity: 0.1;
  border-radius: 50%;
}

.login-form-panel::after {
  content: '';
  position: absolute;
  bottom: -80px;
  left: -80px;
  width: 160px;
  height: 160px;
  background: var(--secondary-color);
  opacity: 0.1;
  border-radius: 50%;
}

.login-form-container {
  max-width: 400px;
  margin: 0 auto;
  width: 100%;
  position: relative;
  z-index: 1;
}

.login-header {
  margin-bottom: 40px;
  text-align: center;
}

.header-icon {
  width: 70px;
  height: 70px;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  box-shadow: 0 10px 20px rgba(67, 97, 238, 0.2);
}

.header-icon i {
  font-size: 30px;
  color: white;
}

.login-header h2 {
  color: var(--text-color);
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 10px;
}

.login-header p {
  color: var(--text-muted);
  font-size: 16px;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-group {
  margin-bottom: 5px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-size: 0.9rem;
  color: var(--text-color);
}

.input-group {
  position: relative;
  display: flex;
  align-items: center;
}

.input-group i {
  position: absolute;
  left: 12px;
  color: var(--text-muted);
}

.input-group input, .input-group select {
  width: 100%;
  padding: 12px 12px 12px 40px;
  border: 1px solid var(--input-border);
  border-radius: 6px;
  background-color: var(--input-bg);
  color: var(--input-text);
  font-size: 1rem;
  transition: border-color 0.3s, box-shadow 0.3s;
}

.input-group input:focus, .input-group select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.2);
  outline: none;
}

.input-group input.is-invalid {
  border-color: var(--danger-color);
  box-shadow: 0 0 0 2px rgba(249, 65, 68, 0.2);
}

.input-group .toggle-password {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
}

.input-group .toggle-password:hover {
  color: var(--primary-color);
}

.error-message {
  margin-top: 5px;
  color: var(--danger-color);
  font-size: 0.8rem;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 5px;
}

.forgot-password {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 0.9rem;
}

.forgot-password:hover {
  text-decoration: underline;
}

.login-button {
  width: 100%;
  padding: 14px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s;
  margin-top: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.login-button:hover {
  background-color: var(--secondary-color);
}

.login-button:disabled {
  background-color: #a0a0a0;
  cursor: not-allowed;
}

.login-footer {
  margin-top: 30px;
  text-align: center;
  color: var(--text-muted);
  font-size: 0.85rem;
}

.support {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 10px;
}

.support a {
  color: var(--primary-color);
  text-decoration: none;
}

.support a:hover {
  text-decoration: underline;
}

.login-footer p {
  color: var(--text-muted);
  font-size: 0.85rem;
}

/* Responsive Design */
@media (max-width: 992px) {
  .login-wrapper {
    flex-direction: column;
  }

  .login-image-panel {
    display: none;
  }

  .login-form-panel {
    padding: 30px 20px;
  }
}

@media (max-width: 576px) {
  .login-form-container {
    padding: 0;
  }

  .login-header h2 {
    font-size: 1.5rem;
  }
}

/* Dark Mode Specific Styles */
:host-context([data-theme="dark"]) .input-group input,
:host-context([data-theme="dark"]) .input-group select {
  background-color: var(--input-bg);
  border-color: var(--input-border);
  color: var(--input-text);
}

:host-context([data-theme="dark"]) .login-form-panel {
  background-color: var(--card-bg-color);
}

:host-context([data-theme="dark"]) .login-header h2,
:host-context([data-theme="dark"]) .form-group label {
  color: var(--text-color);
}

:host-context([data-theme="dark"]) .login-footer,
:host-context([data-theme="dark"]) .login-header p {
  color: var(--text-muted);
}

:host-context([data-theme="dark"]) .input-group i,
:host-context([data-theme="dark"]) .input-group .toggle-password {
  color: var(--text-muted);
}

:host-context([data-theme="dark"]) .login-container {
  background-image: linear-gradient(135deg, #121212 0%, #1a1a1a 100%);
}
