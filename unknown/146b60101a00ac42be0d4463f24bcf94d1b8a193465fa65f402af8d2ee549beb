/* Exercise List Specific Styles */

/* Page Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  border-radius: var(--border-radius-lg);
  color: white;
}

.page-title-container {
  flex: 1;
}

.page-title {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: 1.75rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.page-icon {
  font-size: 1.5rem;
}

.page-subtitle {
  margin: 0;
  opacity: 0.9;
  font-size: 1rem;
}

.page-actions {
  display: flex;
  gap: var(--spacing-sm);
}

/* Responsive Design */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .page-actions {
    width: 100%;
    justify-content: stretch;
  }

  .page-actions .modern-btn {
    flex: 1;
  }
}

@media (max-width: 480px) {
  .page-header {
    padding: var(--spacing-md);
  }

  .page-title {
    font-size: 1.5rem;
  }
}

/* Dark Mode Adjustments */
[data-theme="dark"] .page-header {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
}

/* Filters Section */
.filters-section {
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.filters-section .form-label {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.filters-section .form-select,
.filters-section .form-control {
  border-color: var(--border-color);
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.filters-section .form-select:focus,
.filters-section .form-control:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
}

/* Light mode placeholder styles */
.filters-section .form-control::placeholder {
  color: #6c757d !important;
  opacity: 0.8 !important;
}

.filters-section .form-select::placeholder {
  color: #6c757d !important;
  opacity: 0.8 !important;
}

/* General placeholder styles for light mode */
:not([data-theme="dark"]) input::placeholder {
  color: #6c757d !important;
  opacity: 0.7 !important;
}

:not([data-theme="dark"]) select::placeholder {
  color: #6c757d !important;
  opacity: 0.7 !important;
}

:not([data-theme="dark"]) textarea::placeholder {
  color: #6c757d !important;
  opacity: 0.7 !important;
}

.filters-section .input-group-text {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-secondary);
}

/* Dark mode adjustments for filters */
[data-theme="dark"] .filters-section {
  background: var(--bg-secondary);
  border-color: var(--border-color);
}

[data-theme="dark"] .filters-section .form-label {
  color: var(--text-primary);
}

[data-theme="dark"] .filters-section .form-select,
[data-theme="dark"] .filters-section .form-control {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

[data-theme="dark"] .filters-section .form-select option {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

[data-theme="dark"] .filters-section .form-control::placeholder {
  color: #e9ecef !important;
  opacity: 0.8 !important;
}

[data-theme="dark"] .filters-section .form-select::placeholder {
  color: #e9ecef !important;
  opacity: 0.8 !important;
}

/* Additional placeholder fixes for dark mode with lighter color */
[data-theme="dark"] input::placeholder {
  color: #e9ecef !important;
  opacity: 0.7 !important;
}

[data-theme="dark"] select::placeholder {
  color: #e9ecef !important;
  opacity: 0.7 !important;
}

[data-theme="dark"] textarea::placeholder {
  color: #e9ecef !important;
  opacity: 0.7 !important;
}

/* Specific targeting for exercise list form elements */
[data-theme="dark"] .exercise-list-container input::placeholder,
[data-theme="dark"] .exercise-list-container select::placeholder,
[data-theme="dark"] .exercise-list-container textarea::placeholder {
  color: #e9ecef !important;
  opacity: 0.8 !important;
}

/* Force placeholder visibility for all form controls in dark mode */
[data-theme="dark"] .form-control::placeholder,
[data-theme="dark"] .form-select::placeholder,
[data-theme="dark"] .modern-form-control::placeholder {
  color: #e9ecef !important;
  opacity: 0.8 !important;
}

[data-theme="dark"] .filters-section .input-group-text {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
  color: var(--text-secondary);
}

[data-theme="dark"] .filters-section .btn-outline-secondary {
  color: var(--text-primary);
  border-color: var(--border-color);
}

[data-theme="dark"] .filters-section .btn-outline-secondary:hover {
  background-color: var(--bg-tertiary);
  border-color: var(--text-primary);
  color: var(--text-primary);
}

/* Results Info */
.results-info {
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--border-radius-sm);
  margin-bottom: var(--spacing-md);
  border: 1px solid var(--border-color);
}

.results-info .text-muted {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

/* Loading Container */
.loading-container {
  text-align: center;
  padding: var(--spacing-xl) var(--spacing-lg);
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  margin: var(--spacing-lg) 0;
}

.loading-container p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 0.9rem;
}

/* Exercise Grid Container */
.exercises-container {
  margin-bottom: var(--spacing-lg);
}

/* Exercise Card Styles */
.exercise-card {
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  transition: all var(--transition-speed) var(--transition-timing);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.exercise-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: var(--primary);
}

/* Exercise Type Badge */
.exercise-type-badge {
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  z-index: 2;
}

.exercise-type-badge .badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  vertical-align: middle;
}

/* Light mode badge colors */
.exercise-type-badge .badge-primary {
  background-color: #0d6efd;
  color: white;
}

.exercise-type-badge .badge-info {
  background-color: #0dcaf0;
  color: #000;
}

/* General badge styles for light mode */
.badge-primary {
  background-color: #0d6efd !important;
  color: white !important;
}

.badge-info {
  background-color: #0dcaf0 !important;
  color: #000 !important;
}

.badge-success {
  background-color: #198754 !important;
  color: white !important;
}

.badge-warning {
  background-color: #ffc107 !important;
  color: #000 !important;
}

.badge-danger {
  background-color: #dc3545 !important;
  color: white !important;
}

.badge-secondary {
  background-color: #6c757d !important;
  color: white !important;
}

/* Force badge visibility in all modes */
.badge {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-weight: 600 !important;
  text-align: center !important;
  white-space: nowrap !important;
  vertical-align: middle !important;
  border-radius: 0.375rem !important;
  opacity: 1 !important;
  visibility: visible !important;
  line-height: 1 !important;
}

/* Specific overrides for exercise type badges in light mode */
:not([data-theme="dark"]) .exercise-type-badge .badge-primary {
  background-color: #0d6efd !important;
  color: white !important;
  border: 1px solid #0d6efd !important;
}

:not([data-theme="dark"]) .exercise-type-badge .badge-info {
  background-color: #0dcaf0 !important;
  color: #000 !important;
  border: 1px solid #0dcaf0 !important;
}

/* Ensure all badges are visible in light mode */
:not([data-theme="dark"]) .badge-primary {
  background-color: #0d6efd !important;
  color: white !important;
}

:not([data-theme="dark"]) .badge-info {
  background-color: #0dcaf0 !important;
  color: #000 !important;
}

:not([data-theme="dark"]) .badge-success {
  background-color: #198754 !important;
  color: white !important;
}

:not([data-theme="dark"]) .badge-warning {
  background-color: #ffc107 !important;
  color: #000 !important;
}

:not([data-theme="dark"]) .badge-danger {
  background-color: #dc3545 !important;
  color: white !important;
}

:not([data-theme="dark"]) .badge-secondary {
  background-color: #6c757d !important;
  color: white !important;
}

/* Exercise Header */
.exercise-header {
  padding: var(--spacing-lg) var(--spacing-md) var(--spacing-sm);
  border-bottom: 1px solid var(--border-color);
  flex-shrink: 0;
}

.exercise-header-content {
  padding-right: 4rem; /* Space for type badge */
}

.exercise-name {
  margin: 0 0 var(--spacing-xs) 0;
  font-weight: 700;
  color: var(--text-primary);
  font-size: 1.1rem;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.exercise-meta {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-xs);
}

.category-badge {
  background: rgba(13, 110, 253, 0.1);
  color: #0d6efd;
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius-sm);
  font-size: 0.75rem;
  font-weight: 600;
  border: 1px solid rgba(13, 110, 253, 0.2);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  opacity: 1;
  visibility: visible;
  line-height: 1;
}

/* Light mode category badge override */
:not([data-theme="dark"]) .category-badge {
  background: rgba(13, 110, 253, 0.15) !important;
  color: #0d6efd !important;
  border: 1px solid rgba(13, 110, 253, 0.3) !important;
}

.difficulty-badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  vertical-align: middle;
}

/* Difficulty badge specific colors for light mode */
.difficulty-badge.badge-success {
  background-color: #198754 !important;
  color: white !important;
}

.difficulty-badge.badge-warning {
  background-color: #ffc107 !important;
  color: #000 !important;
}

.difficulty-badge.badge-danger {
  background-color: #dc3545 !important;
  color: white !important;
}

.difficulty-badge.badge-secondary {
  background-color: #6c757d !important;
  color: white !important;
}

/* Exercise Content */
.exercise-content {
  padding: var(--spacing-md);
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.exercise-description {
  color: var(--text-secondary);
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: var(--spacing-sm);
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.exercise-details {
  margin-bottom: var(--spacing-sm);
}

.detail-item {
  font-size: 0.85rem;
  margin-bottom: var(--spacing-xs);
  color: var(--text-secondary);
}

.detail-item strong {
  color: var(--text-primary);
  font-weight: 600;
}

.exercise-instructions {
  margin-top: auto;
}

.exercise-instructions strong {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 0.85rem;
}

.instructions-text {
  font-size: 0.85rem;
  color: var(--text-secondary);
  margin: var(--spacing-xs) 0 0 0;
  line-height: 1.4;
}

/* Exercise Actions */
.exercise-actions {
  padding: var(--spacing-sm) var(--spacing-md);
  border-top: 1px solid var(--border-color);
  display: flex;
  gap: var(--spacing-xs);
  justify-content: flex-end;
  background: var(--bg-secondary);
}

.exercise-actions .btn {
  border-radius: var(--border-radius-sm);
  padding: 0.375rem 0.75rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .exercise-name {
    font-size: 1rem;
  }
}

@media (max-width: 768px) {
  .exercise-header {
    padding: var(--spacing-md) var(--spacing-sm) var(--spacing-xs);
  }

  .exercise-content {
    padding: var(--spacing-sm);
  }

  .exercise-actions {
    padding: var(--spacing-xs) var(--spacing-sm);
  }

  .exercise-header-content {
    padding-right: 3rem;
  }
}

/* Dark mode adjustments */
[data-theme="dark"] .exercise-card {
  background: var(--bg-secondary);
  border-color: var(--border-color);
}

[data-theme="dark"] .exercise-card:hover {
  border-color: var(--primary);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .exercise-header {
  border-color: var(--border-color);
}

[data-theme="dark"] .exercise-name {
  color: var(--text-primary);
}

[data-theme="dark"] .exercise-description {
  color: var(--text-secondary);
}

[data-theme="dark"] .detail-item {
  color: var(--text-secondary);
}

[data-theme="dark"] .detail-item strong {
  color: var(--text-primary);
}

[data-theme="dark"] .exercise-instructions strong {
  color: var(--text-primary);
}

[data-theme="dark"] .instructions-text {
  color: var(--text-secondary);
}

[data-theme="dark"] .exercise-actions {
  border-color: var(--border-color);
  background: var(--bg-tertiary);
}

[data-theme="dark"] .category-badge {
  background: var(--primary-dark);
  color: var(--primary-light);
}

[data-theme="dark"] .difficulty-badge.badge-success {
  background-color: var(--success);
  color: white;
}

[data-theme="dark"] .difficulty-badge.badge-warning {
  background-color: var(--warning);
  color: white;
}

[data-theme="dark"] .difficulty-badge.badge-danger {
  background-color: var(--danger);
  color: white;
}

[data-theme="dark"] .difficulty-badge.badge-secondary {
  background-color: var(--secondary);
  color: white;
}

[data-theme="dark"] .exercise-type-badge .badge-primary {
  background-color: var(--primary);
  color: white;
}

[data-theme="dark"] .exercise-type-badge .badge-info {
  background-color: var(--info);
  color: white;
}

/* Additional badge styles for dark mode */
[data-theme="dark"] .badge {
  color: white;
}

[data-theme="dark"] .badge-primary {
  background-color: var(--primary) !important;
  color: white !important;
}

[data-theme="dark"] .badge-info {
  background-color: var(--info) !important;
  color: white !important;
}

[data-theme="dark"] .badge-success {
  background-color: var(--success) !important;
  color: white !important;
}

[data-theme="dark"] .badge-warning {
  background-color: var(--warning) !important;
  color: white !important;
}

[data-theme="dark"] .badge-danger {
  background-color: var(--danger) !important;
  color: white !important;
}

[data-theme="dark"] .badge-secondary {
  background-color: var(--secondary) !important;
  color: white !important;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: var(--spacing-xl) var(--spacing-lg);
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  margin: var(--spacing-lg) 0;
}

.empty-icon {
  color: var(--text-muted);
  margin-bottom: var(--spacing-md);
}

.empty-state h4 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.empty-state p {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
}

/* Pagination */
.pagination-nav {
  margin-top: var(--spacing-lg);
  padding: var(--spacing-md) 0;
}

.pagination {
  margin: 0;
}

.page-link {
  color: var(--text-primary);
  background-color: var(--bg-primary);
  border-color: var(--border-color);
  padding: 0.5rem 0.75rem;
  margin: 0 0.125rem;
  border-radius: var(--border-radius-sm);
}

.page-link:hover {
  color: var(--primary);
  background-color: var(--bg-secondary);
  border-color: var(--primary);
}

.page-item.active .page-link {
  background-color: var(--primary);
  border-color: var(--primary);
  color: white;
}

.page-item.disabled .page-link {
  color: var(--text-muted);
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
}

/* Dark mode adjustments for empty state and pagination */
[data-theme="dark"] .empty-state {
  background: var(--bg-secondary);
  border-color: var(--border-color);
}

[data-theme="dark"] .empty-state h4 {
  color: var(--text-primary);
}

[data-theme="dark"] .empty-state p {
  color: var(--text-secondary);
}

[data-theme="dark"] .empty-icon {
  color: var(--text-muted);
}

[data-theme="dark"] .page-link {
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
}

[data-theme="dark"] .page-link:hover {
  color: var(--primary-light);
  background-color: var(--bg-tertiary);
  border-color: var(--primary);
}

[data-theme="dark"] .page-item.active .page-link {
  background-color: var(--primary);
  border-color: var(--primary);
  color: white;
}

[data-theme="dark"] .page-item.disabled .page-link {
  color: var(--text-muted);
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

/* Dark mode adjustments for results info and loading */
[data-theme="dark"] .results-info {
  background: var(--bg-tertiary);
  border-color: var(--border-color);
}

[data-theme="dark"] .results-info .text-muted {
  color: var(--text-secondary);
}

[data-theme="dark"] .loading-container {
  background: var(--bg-secondary);
  border-color: var(--border-color);
}

[data-theme="dark"] .loading-container p {
  color: var(--text-secondary);
}
