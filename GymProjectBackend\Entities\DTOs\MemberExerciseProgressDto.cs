using Core.Entities;
using System;

namespace Entities.DTOs
{
    /// <summary>
    /// Egzersiz ilerleme ekleme DTO'su
    /// </summary>
    public class MemberExerciseProgressAddDto : IDto
    {
        public int MemberWorkoutProgramID { get; set; }
        public int WorkoutProgramExerciseID { get; set; }
        public int CompletedSets { get; set; }
        public string? ActualReps { get; set; }
        public string? Notes { get; set; }
    }

    /// <summary>
    /// Egzersiz ilerleme görüntüleme DTO'su
    /// </summary>
    public class MemberExerciseProgressDto : IDto
    {
        public int MemberExerciseProgressID { get; set; }
        public int MemberWorkoutProgramID { get; set; }
        public int WorkoutProgramExerciseID { get; set; }
        public int MemberID { get; set; }
        public string MemberName { get; set; }
        public string ExerciseName { get; set; }
        public string? CategoryName { get; set; }
        public DateTime CompletedDate { get; set; }
        public int CompletedSets { get; set; }
        public string? ActualReps { get; set; }
        public string? Notes { get; set; }
        public DateTime? CreationDate { get; set; }
    }

    /// <summary>
    /// Egzersiz ilerleme listesi DTO'su
    /// </summary>
    public class MemberExerciseProgressListDto : IDto
    {
        public int MemberExerciseProgressID { get; set; }
        public int MemberID { get; set; }
        public string MemberName { get; set; }
        public string ExerciseName { get; set; }
        public string? CategoryName { get; set; }
        public DateTime CompletedDate { get; set; }
        public int CompletedSets { get; set; }
        public string? ActualReps { get; set; }
    }

    /// <summary>
    /// Üye egzersiz istatistikleri DTO'su
    /// </summary>
    public class MemberExerciseProgressStatsDto : IDto
    {
        public int MemberID { get; set; }
        public string MemberName { get; set; }
        public int TotalCompletedExercises { get; set; }
        public int TotalCompletedSets { get; set; }
        public DateTime? LastWorkoutDate { get; set; }
        public int WeeklyCompletedExercises { get; set; }
        public int MonthlyCompletedExercises { get; set; }
        public double CompletionPercentage { get; set; } // Program tamamlama yüzdesi
    }

    /// <summary>
    /// Mobil API için egzersiz tamamlama durumu DTO'su
    /// </summary>
    public class ExerciseCompletionStatusDto : IDto
    {
        public int WorkoutProgramExerciseID { get; set; }
        public bool IsCompleted { get; set; }
        public DateTime? CompletedDate { get; set; }
        public int? CompletedSets { get; set; }
        public string? ActualReps { get; set; }
        public string? Notes { get; set; }
    }

    /// <summary>
    /// Günlük antrenman ilerleme DTO'su
    /// </summary>
    public class DailyWorkoutProgressDto : IDto
    {
        public DateTime WorkoutDate { get; set; }
        public int TotalExercises { get; set; }
        public int CompletedExercises { get; set; }
        public double CompletionPercentage { get; set; }
        public int TotalSets { get; set; }
        public int CompletedSets { get; set; }
    }

    /// <summary>
    /// Egzersiz ilerleme güncelleme DTO'su
    /// </summary>
    public class MemberExerciseProgressUpdateDto : IDto
    {
        public int MemberExerciseProgressID { get; set; }
        public int CompletedSets { get; set; }
        public string? ActualReps { get; set; }
        public string? Notes { get; set; }
    }
}
