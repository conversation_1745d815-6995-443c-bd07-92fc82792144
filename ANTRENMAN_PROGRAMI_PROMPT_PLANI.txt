🎯 ANTRENMAN PROGRAMI SİSTEMİ - KAPSAMLI PROMPT LİSTESİ
================================================================

PROJE: GymKod Pro - Multi-tenant Spor Salonu Yönetim Sistemi
HEDEF: 10.000+ kullanıcı için antrenman programı sistemi
YAKLASIM: Backend + Frontend birlikte (test kolaylığı için)

================================================================
PROMPT 1: Egzersiz Kategorileri ve Kapsamlı Egzersiz Veritabanı 
================================================================

AMAÇ:
Sistem genelinde kullanılacak KAPSAMLI egzersiz altyapısını kurmak

SORUN:
Sistem genelinde kullanılabilir egzersiz veritabanı yok
Salon hocaları sadece görüntüleyebileceği için TÜM FİTNESS SPOR SALONLARINA YAPILABİLECEK EGZERSİZLERİ sisteme yüklememiz gerekiyor. türkçe karşılığı mantıklı olan egzersizlerin yanına parantez içinde türkçesini yazalım örneğin push up(şınav) gibi mantıklı türkçe litarütüründe olan kelimelerle uyumlu hareket varsa yanına parantez içinde türkçesini de yaz ki sistemi kullanan kişiler hareketin ingilizcesini bilmiyorsa bile türkçesiyle sorunu kapatabilsin. ingilizce hareketleri türkçeye çevirdiğmiizde anlamsız isim çıkacaksa o hareketlerin yanına türkçesini yazma örneğin Bench Press" (Türkçesi "Bank Presi" anlamsız).
hareketleri ayarlarken makine + dumbell + vücut ağırlığı içeren hareketleri koyma odaklı yap mesela fly olarak dumbell fly var bide kelebek makinesinde fly var
Egzersiz kategorileri sistemi yok
salon sahibi mevcut sistem tarafından tanımlanan egzersizleri yetersiz görüp kendisi eklemek isterse yeni egzersiz ekle butonuna basabilmeli ve sadece ekleyen salon sahibi kendi panelinde görebilmeli örneğin a salonu yeni bir egzersiz eklerse b salonu bu egzersizi görememeli. sadece sistem tarafından ayarlanan egzersizleri herkese görülmeli.

İSTEDİĞİMİZ:
KAPSAMLI egzersiz veritabanı - Fitness salonlarında yapılan TÜM egzersizler
Egzersizleri kategoriye ayıracağız örneğin göğüs bacak sırt omuz tarzında bütün bölgelerin kategorisiyle egzersizleri birleştireceğiz ki spor salonu sahibi filtreleme yapabilsin.

KAPSAM:
ExerciseCategories tablosu
Exercises tablosu
Backend: Egzersiz görüntüleme servisleri (SADECE READ)
Frontend: Admin panelinde egzersiz listesi (sadece görüntüleme + arama + filtreleme)
Kapsamlı Seed Data: Fitness dünyasındaki bilinen tüm egzersizler

TEST:
Admin panelinden egzersizleri kategorilere göre filtreleyip, arama yaparak istediği egzersizi bulabilmeli.

================================================================
PROMPT 2: Antrenman Programı Şablonu Sistemi
================================================================

AMAÇ: Salon sahiplerinin egzersizlerden program oluşturabilmesi. bu sistemi kurma sebebimiz spor salonu hocalarının egzersizleri program haline getirip ilerde salon üyelerine bu programları entegre edip üyelere antrenman programını görebilmelerini sağlamak için. şimdilik aşağıdakileri yapalım.

SORUN:
- Salon sahipleri antrenman programı oluşturamıyor
- Sistem egzersizlerinden program yapma sistemi yok
- Günlere bölerek program organize etme yok
- Her egzersiz için set/tekrar belirleme sistemi yok
- Gün isimlerini özelleştirme yok ("Sırt-Önkol" vb.)

İSTEDİĞİMİZ:
- Admin panelinde "Antrenman Programları" menüsü
- Program şablonu listesi (sadece kendi salonunun programları)
- Yeni program oluşturma sistemi:
  * Program adı girişi (örn: "Başlangıç Fitness", "Kilo Verme Programı")
  * Program açıklaması
  * Deneyim seviyesi seçimi (Başlangıç/Orta/İleri)
  * Hedef seçimi (Kilo Alma/Kilo Verme/Kas Yapma)
  * Program süresi (kaç hafta)
- Gün ekleme sistemi:
  * Gün sayısı belirleme (3 gün, 5 gün vb.)
  * Her güne özel isim verme ("Sırt-Önkol", "Göğüs-Arka Kol" vb.)
  * Dinlenme günü işaretleme
- Egzersiz ekleme sistemi:
  * Sistem egzersizlerinden seçim (dropdown/modal)
  * Her egzersiz için set sayısı girişi
  * Her egzersiz için tekrar sayısı girişi (12, MAX, 12-15 vb.)
  * Egzersiz notları
  * Egzersiz sıralaması (drag&drop)

KAPSAM:
- WorkoutProgramTemplate, WorkoutProgramDay, WorkoutProgramExercise için backend servisleri
- Program şablonu CRUD işlemleri
- Frontend: Program listesi component'i
- Program oluşturma sayfası:
  * Program bilgileri formu
  * Gün ekleme/düzenleme sistemi
  * Egzersiz seçim modal'ı (sistem egzersizlerinden)
  * Set/tekrar girişi
  * Drag&drop sıralama

TEST: Admin "Başlangıç Fitness" programı oluşturup, "Sırt-Önkol" günü ekleyip, sistem egzersizlerinden seçerek set/tekrar belirleyebilmeli. kaydettiğinde panelinde programlarım bölümünde görebilmeli. gördükten sonra güncelleyebilmeli.

================================================================
PROMPT 3: Program Atama Sistemi 
================================================================

AMAÇ: Üyelere antrenman programı atayabilmek

SORUN:
- Üyelere program atama sistemi yok
- Program atama geçmişi görülmüyor
- Bir üyeye kaç program atandığı bilinmiyor

İSTEDİĞİMİZ:
- Program atama sistemi:
  * Üye seçimi (salon üyelerinden seçim)
  * Program seçimi (kendi salonunun programları)
  * Notlar ekleyebilme
- Bir üyeye istediği kadar program atayabilme
- Program atama geçmişi

KAPSAM:
- MemberWorkoutPrograms tablosu (üye-program ilişkisi)
- Backend: Program atama servisleri
- Frontend: Program atama paneli (üye seçimi + program seçimi + tarih)
- Atama geçmişi görüntüleme

TEST: Admin üyeye program atayıp, üye detayında aktif programları görebilmeli.

================================================================
PROMPT 4: Mobil API ve User-Member İlişkisi (TEMEL)
================================================================

AMAÇ: Mobil uygulamada temel antrenman programı görüntüleme sistemini kurmak

SORUN:
- Üyeler antrenman programı paneline girdiğinde boş sayfa görüyor
- Salon hocası tarafından atanan programları göremiyor
- Program atanmamış durumda uyarı yok
- Mobil API entegrasyonu eksik

İSTEDİKLERİM:
- Üyeler login olduğunda yetkisi member ise antrenman programı panelini görebiliyor
- Eğer salon hocası üyeye program atamamışsa "Salon yöneticinize danışın" uyarısı çıksın
- Salon hocası program atamışsa üye programını güzel arayüzle görebilsin
- Backend'deki mevcut API'leri mobile entegre et

KAPSAM:
- Mobil antrenman programı provider'ı
- Backend API entegrasyonu (GetActiveWorkoutProgramsByUserId)
- Program görüntüleme UI bileşenleri
- "Program yok" durumu için uyarı ekranı
- Mevcut tasarım sistemine uygun arayüz

TEST: Üye mobilden hesabına giriş yaptığında antrenman programı sekmesinde atanan programları görebilmeli, program yoksa uyarı görmeli.

================================================================
PROMPT 5: Egzersiz İlerleme Takip Sistemi
================================================================

AMAÇ: Üyelerin egzersiz ilerlemelerini takip edebilmesi

SORUN:
- Üyeler hangi egzersizi yaptığını hatırlamıyor
- İlerleme takibi yok
- Egzersiz tamamlama sistemi yok

İSTEDİKLERİM:
- Üye egzersizi bitirince solundaki butona tıklarsa üstü çizilsin ve tiklensin
- Hangi harekette kaldığını hatırlasın
- İlerlemesini görebilsin
- Günlük bazda ilerleme takibi

KAPSAM:
- MemberExerciseProgress tablosu ve migration
- İlerleme takip backend servisleri
- Mobil egzersiz tamamlama UI
- Checkbox/tick sistemi
- İlerleme göstergesi

TEST: Üye egzersizi tamamladığında işaretleyebilmeli, bir sonraki girişinde durumu hatırlanmalı.

================================================================
PERFORMANS OPTİMİZASYONU (TÜM PROMPTLARDA):
================================================================

Veritabanı tablolarını oluştururken index kullanılması gereken yer varsa migration içinde indexi de ayarla amacımız 10000 kullanıcının sıkıntısız kasmadan ve donma olmadan sistemi kullanabilmesini sağlamak.

Cache sistemini de en uygun nasılsa hangi promptta olursak olalım eklenmesi gereken yere ekle.
Pagination gerekiyorsa yap lazyloading gereken kısımlar varsa uygula
Performance optimizasyonunu da her promptta uygula

================================================================
TEST STRATEJİSİ:
================================================================

Her prompt tamamlandıktan sonra projeleri buildle ve kodlarda hata var mı kontrol et daha sonra bana rapor çıkar ve sistemi test etmemi bekle. 
HEDEF: Türkiye çapında 10.000+ kullanıcının sorunsuz kullanabileceği sistem

================================================================
İŞ KURALLARI:
================================================================

- Bir üyeye salon sahibi istediği kadar program atayabilir
- Egzersizler sistem genelinde tanımlı (salon sahipleri eğer aradığı bir hareket yoksa ve eksik bir hareket gördüyse kendisi sistemine egzersizi ekleyebilir ama bu sadece salon sahibi tarafından kendi panelinde ona görünür diğer salon sahiplerine görünmez.)
- Hazır programlar sistem genelinde paylaşılır
- Admin ve owner yetkili kullanıcılar program oluşturabilir
- Program atama Member tablosu üzerinden yapılır (salon sahipleri kendi üyelerine atar)

================================================================
PROMPT YÖNETİM SİSTEMİ:
================================================================

TAMAMLANAN PROMPTLAR:
- [x] PROMPT 1: Egzersiz Kategorileri ve Kapsamlı Egzersiz Veritabanı 
- [x] PROMPT 2: Antrenman Programı Şablonu Sistemi
- [x] PROMPT 3: Program Atama Sistemi
- [ ] PROMPT 4: Mobil API ve User-Member İlişkisi (ŞU ANDA)
- [ ] PROMPT 5: Egzersiz İlerleme Takip Sistemi

ŞU ANDA YAPILACAK: PROMPT 4'ü tamamlayıp test edeceğiz. Backend kısmını bitirince projeyi build'leyip rapor vereceksin , sonra mobil için onay bekleyeceksin.
