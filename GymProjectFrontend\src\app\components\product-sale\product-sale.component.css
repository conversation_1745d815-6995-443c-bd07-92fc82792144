.content-blur {
    filter: blur(2px);
    pointer-events: none;
  }
  
  .mat-form-field {
    width: 100%;
  }

  /* Search Input Styles */
  .search-input-container {
    position: relative;
    margin-bottom: 1rem;
  }
  
  .search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
  }
  
  .search-input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border-radius: 0.5rem;
    border: 1px solid var(--border-color);
    background-color: var(--input-bg);
    color: var(--input-text);
    transition: all 0.3s ease;
  }
  
  .search-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
    outline: none;
  }
  
  /* Dark Mode Support */
  [data-theme="dark"] .search-input {
    background-color: #4a5568;
    border-color: #4a5568;
    color: #e2e8f0;
  }
  
  [data-theme="dark"] .search-input:focus {
    background-color: #4a5568;
    border-color: #63b3ed;
    color: #e2e8f0;
  }
  
  .table thead th {
    background-color: #4a7299;
    color: white;
    padding: 12px;
  }
  
  .table td {
    vertical-align: middle;
    padding: 10px;
  }
  
  .cart-table {
    margin-top: 20px;
  }
  
  .total-row {
    background-color: #f8f9fa;
    font-weight: bold;
  }
  
  .action-buttons {
    display: flex;
    gap: 10px;
    margin-top: 20px;
  }
  
  .quantity-input {
    width: 80px !important;
  }
  
  .product-select {
    flex-grow: 1;
  }
  
  .cart-container {
    margin-top: 30px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 20px;
  }
  
  .cart-header {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #dee2e6;
  }
  
  .cart-footer {
    margin-top: 15px;
    padding-top: 10px;
    border-top: 2px solid #dee2e6;
  }
  
  .member-info {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
  }
  
  .member-info strong {
    color: #4a7299;
  }