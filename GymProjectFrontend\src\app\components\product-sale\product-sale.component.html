<div class="container mt-4">
  <div class="d-flex justify-content-center align-items-center" *ngIf="isLoading">
      <app-loading-spinner></app-loading-spinner>
  </div>

  <div class="fade-in" [class.content-blur]="isLoading">
    <div class="row">
      <!-- Sol Panel - Satış Formu -->
      <div class="col-lg-7 mb-4">
        <div class="modern-card slide-in-left">
          <div class="modern-card-header">
            <h5><PERSON>rün Satış Formu</h5>
          </div>
          <div class="modern-card-body">
            <form [formGroup]="saleForm" class="row g-3">
              <!-- Üye Seçimi -->
              <div class="col-12">
                <label class="modern-form-label"><PERSON>ye Seçimi</label>
                <div class="search-input-container">
                  <i class="fas fa-search search-icon"></i>
                  <input type="text"
                         class="search-input"
                         placeholder="<PERSON>ye adı veya telefon numarası"
                         formControlName="member"
                         [matAutocomplete]="autoMember"
                         (blur)="validateMemberSelection()">
                  <mat-autocomplete #autoMember="matAutocomplete" [displayWith]="displayMember">
                    <mat-option *ngFor="let member of filteredMembers | async" [value]="member">
                      {{member.name}} - {{member.phoneNumber}}
                    </mat-option>
                  </mat-autocomplete>
                </div>
                <div *ngIf="memberSelectionInvalid && saleForm.get('member')?.touched" class="text-danger mt-1">
                  <small>Lütfen listeden bir üye seçiniz</small>
                </div>
              </div>

              <!-- Ürün Seçimi ve Miktar -->
              <div class="col-md-8">
                <label class="modern-form-label">Ürün Seçimi</label>
                <div class="search-input-container">
                  <i class="fas fa-box search-icon"></i>
                  <input type="text"
                         class="search-input"
                         placeholder="Ürün adı"
                         formControlName="product"
                         [matAutocomplete]="autoProduct"
                         (blur)="validateProductSelection()">
                  <mat-autocomplete #autoProduct="matAutocomplete" [displayWith]="displayProduct">
                    <!-- Değişiklik burada -->
                    <mat-option *ngFor="let product of filteredProducts | async" [value]="product">
                      {{product.name}} - {{product.price | currency:'TRY':'symbol-narrow':'1.2-2'}}
                    </mat-option>
                  </mat-autocomplete>
                </div>
                <!-- Hata mesajını sadece dokunulduğunda ve geçersizse göster -->
                <div *ngIf="productSelectionInvalid && saleForm.get('product')?.touched" class="text-danger mt-1">
                  <small>Lütfen listeden bir ürün seçiniz</small>
                </div>
              </div>

              <div class="col-md-4">
                <label class="modern-form-label">Miktar</label>
                <div class="search-input-container">
                  <i class="fas fa-hashtag search-icon"></i>
                  <input type="number"
                         class="search-input"
                         placeholder="Miktar"
                         min="1"
                         formControlName="quantity">
                </div>
                 <!-- Miktar için hata mesajı (opsiyonel) -->
                 <div *ngIf="saleForm.get('quantity')?.invalid && saleForm.get('quantity')?.touched" class="text-danger mt-1">
                    <small *ngIf="saleForm.get('quantity')?.errors?.['required']">Miktar gerekli</small>
                    <small *ngIf="saleForm.get('quantity')?.errors?.['min']">Miktar en az 1 olmalı</small>
                 </div>
              </div>

              <!-- Sepete Ekle Butonu -->
              <div class="col-12">
                <button class="modern-btn modern-btn-primary w-100"
                        [disabled]="productSelectionInvalid || !saleForm.get('product')?.value || typeof saleForm.get('product')?.value === 'string' || saleForm.get('quantity')?.invalid"
                        (click)="addToCart()">
                  <i class="fas fa-cart-plus modern-btn-icon"></i>
                  Sepete Ekle
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>

      <!-- Sağ Panel - Sepet -->
      <div class="col-lg-5">
        <div class="modern-card slide-in-right">
          <div class="modern-card-header">
            <h5>Sepet</h5>
            <span class="modern-badge modern-badge-primary" style="font-size: 1rem;">
              {{totalAmount | currency:'TRY':'symbol-narrow':'1.2-2'}}
            </span>
          </div>

          <div class="modern-card-body">
            <!-- Boş Sepet -->
            <div *ngIf="cartItems.length === 0" class="text-center py-5">
              <i class="fas fa-shopping-cart fa-3x text-primary mb-3"></i>
              <p class="text-primary">Sepetiniz boş</p>
            </div>

            <!-- Sepet İçeriği -->
            <div *ngIf="cartItems.length > 0">
              <div class="cart-items">
                <div *ngFor="let item of cartItems; let i = index"
                     class="cart-item p-3 mb-2 zoom-in"
                     style="background-color: var(--bg-secondary); border-radius: var(--border-radius-md);">
                  <div class="d-flex justify-content-between align-items-center">
                    <div>
                      <h6 class="mb-1">{{getProductName(item.productId)}}</h6>
                      <div class="d-flex align-items-center">
                        <span class="modern-badge modern-badge-info me-2">
                          {{item.quantity}} adet
                        </span>
                        <span class="text-muted">
                          {{item.unitPrice | currency:'TRY':'symbol-narrow':'1.2-2'}}
                        </span>
                      </div>
                    </div>
                    <div class="d-flex align-items-center">
                      <span class="me-3 fw-bold">
                        {{item.quantity * item.unitPrice | currency:'TRY':'symbol-narrow':'1.2-2'}}
                      </span>
                      <button class="modern-btn modern-btn-danger modern-btn-sm" (click)="removeFromCart(i)">
                        <i class="fas fa-times"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Satış Tamamla -->
              <div class="mt-4">
                <button class="modern-btn modern-btn-success w-100"
                        [disabled]="memberSelectionInvalid || !saleForm.get('member')?.value || typeof saleForm.get('member')?.value === 'string' || cartItems.length === 0"
                        (click)="sell()">
                  <i class="fas fa-check-circle modern-btn-icon"></i>
                  Satışı Tamamla
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
