<div class="container mt-4">
    <div class="card">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h3><PERSON>ns <PERSON></h3>
        <button mat-raised-button color="primary" (click)="openAddDialog()">
          <i class="fas fa-plus me-2"></i>Yeni Lisans Paketi
        </button>
      </div>
  
      <div class="card-body">
        <div *ngIf="isLoading" class="d-flex justify-content-center">
          <app-loading-spinner></app-loading-spinner>
        </div>
  
        <div *ngIf="!isLoading && licensePackages.length === 0" class="alert alert-info">
          Henüz lisans paketi bulunmamaktadır.
        </div>
  
        <div *ngIf="!isLoading && licensePackages.length > 0" class="table-responsive">
          <table mat-table [dataSource]="licensePackages" class="w-100">
            <!-- Name Column -->
            <ng-container matColumnDef="name">
              <th mat-header-cell *matHeaderCellDef>Paket Adı</th>
              <td mat-cell *matCellDef="let item">{{ item.name }}</td>
            </ng-container>
  
            <!-- Description Column -->
            <ng-container matColumnDef="description">
              <th mat-header-cell *matHeaderCellDef>Açıklama</th>
              <td mat-cell *matCellDef="let item">{{ item.description }}</td>
            </ng-container>
  
            <!-- Role Column -->
            <ng-container matColumnDef="role">
              <th mat-header-cell *matHeaderCellDef>Rol</th>
              <td mat-cell *matCellDef="let item">{{ item.role }}</td>
            </ng-container>
  
            <!-- Duration Column -->
            <ng-container matColumnDef="durationDays">
              <th mat-header-cell *matHeaderCellDef>Süre (gün)</th>
              <td mat-cell *matCellDef="let item">{{ item.durationDays }}</td>
            </ng-container>
  
            <!-- Price Column -->
            <ng-container matColumnDef="price">
              <th mat-header-cell *matHeaderCellDef>Fiyat</th>
              <td mat-cell *matCellDef="let item">{{ item.price | currency:'TRY':'symbol':'1.2-2' }}</td>
            </ng-container>
  
            <!-- Actions Column -->
            <ng-container matColumnDef="actions">
              <th mat-header-cell *matHeaderCellDef>İşlemler</th>
              <td mat-cell *matCellDef="let item">
                <button mat-icon-button color="primary" (click)="openEditDialog(item)" matTooltip="Düzenle">
                  <i class="fas fa-edit"></i>
                </button>
                <button mat-icon-button color="warn" (click)="deleteLicensePackage(item.licensePackageID, item.name)" matTooltip="Sil">
                  <i class="fas fa-trash-alt"></i>
                </button>
              </td>
            </ng-container>
  
            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
          </table>
        </div>
      </div>
    </div>
  </div>
  