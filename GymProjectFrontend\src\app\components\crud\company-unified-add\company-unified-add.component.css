/* Blur effect for loading state */
.content-blur {
  filter: blur(3px);
  pointer-events: none;
}

/* Full width form fields */
mat-form-field {
  width: 100%;
  margin-bottom: 8px;
}

/* Section styling */
.form-section {
  margin-bottom: 1.5rem;
  padding: 1.25rem;
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-md);
  transition: all 0.3s ease;
}

.form-section:hover {
  background-color: var(--bg-tertiary);
  box-shadow: var(--shadow-sm);
}

.form-section-title {
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--primary);
  border-bottom: 2px solid var(--primary-light);
  padding-bottom: 0.5rem;
}

/* Table styling */
.table {
  width: 100%;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.table th,
.table td {
  padding: 0.75rem;
  vertical-align: middle;
  border-top: 1px solid var(--border-color);
}

.table thead th {
  vertical-align: bottom;
  border-bottom: 2px solid var(--border-color);
  background-color: var(--bg-secondary);
}

.table tbody tr:hover {
  background-color: var(--primary-light);
}

/* Button spacing */
.btn {
  margin-right: 0.5rem;
}

.btn:last-child {
  margin-right: 0;
}

/* Card styling */
.card {
  margin-bottom: 1.5rem;
  border: none;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card-header {
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  padding: 1rem 1.25rem;
}

.card-body {
  padding: 1.25rem;
}

/* Responsive table */
@media (max-width: 767.98px) {
  .table-responsive {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
