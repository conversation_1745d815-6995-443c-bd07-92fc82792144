<div class="gym-container">
  <div class="gym-panel">
    <div class="header">
      <div class="gym-icon">
        <i class="fas fa-qrcode"></i>
      </div>
    </div>

    <div *ngIf="isLoading" class="message-area">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i> Yükleniyor...
      </div>
    </div>

    <div *ngIf="isError" class="message-area error">
      <i class="fas fa-exclamation-circle"></i> {{ message }}
      <p class="error-help">
        Eğer bir spor salonuna üye olduysanız ve QR kodunuzu göremiyorsanız, lütfen spor salonunuzla iletişime geçin.
      </p>
    </div>

    <div *ngIf="memberInfo" class="result-area" [ngClass]="{'special-member': isSpecialMember}">
      <div *ngIf="isSpecialMember" class="special-header">
        <div class="hearts">
          <span>❤️</span>
          <span>❤️</span>
          <span>❤️</span>
        </div>
        <h2>Canım Sevgilim 😊</h2>
        <div class="hearts">
          <span>❤️</span>
          <span>❤️</span>
          <span>❤️</span>
        </div>
      </div>
      <h2 *ngIf="!isSpecialMember">{{ memberInfo.name }}</h2>

      <div class="membership-info" [ngClass]="{'special-info': isSpecialMember}">
        <div
          *ngFor="let membership of getFutureMemberships()"
          class="future-membership"
        >
          <i class="fas fa-calendar-plus"></i> {{ membership.branch }} {{ message }}
        </div>

        <div
          *ngFor="let membership of getActiveMemberships()"
          class="remaining-days"
        >
          <i class="fas fa-calendar-check"></i> {{ membership.branch }} üyeliğinizin bitmesine
          <strong>{{ membership.remainingDays }}</strong> gün kalmıştır.
        </div>

        <div
          *ngFor="let membership of getExpiredMemberships()"
          class="expired-membership"
        >
          <i class="fas fa-calendar-times"></i> {{ membership.branch }} üyeliğiniz sona ermiştir
        </div>

        <div
          *ngIf="memberInfo.memberships && memberInfo.memberships.length === 0"
          [ngClass]="{'expired-membership-special': !isSpecialMember, 'special-expired-text': isSpecialMember}"
        >
          <i class="fas fa-calendar-times"></i> Üyeliğiniz sona ermiştir
        </div>

        <div *ngIf="memberInfo.isFrozen" class="frozen-membership">
          <i class="fas fa-snowflake"></i> Üyeliğiniz dondurulmuştur.
          <p *ngIf="memberInfo.freezeEndDate">Açılış Tarihi: {{ memberInfo.freezeEndDate | date:'dd/MM/yyyy' }}</p>
        </div>
      </div>

      <div *ngIf="memberInfo.scanNumber && !memberInfo.isFrozen && hasActiveMembership()" class="qr-code-container">
        <qrcode
          [qrdata]="memberInfo.scanNumber"
          [width]="244"
          [errorCorrectionLevel]="'M'"
        ></qrcode>

        <!-- QR Kod Geçerlilik Süresi Göstergesi -->
        <div class="qr-validity-timer">
          <div class="timer-label">
            <span>QR Kod Geçerlilik Süresi:</span>
            <span class="timer-countdown" [ngClass]="{'timer-warning': remainingSeconds < 60}">
              {{ getFormattedRemainingTime() }}
            </span>
          </div>
          <div class="progress-bar-container">
            <div class="progress-bar" [style.width.%]="getRemainingTimePercentage()"
                 [ngClass]="{'progress-warning': remainingSeconds < 60}"></div>
          </div>
        </div>



        <div class="qr-info-text">
          <i class="fas fa-info-circle"></i> Bu QR kod 5 dakika geçerlidir ve süre sonunda otomatik olarak yenilenir.
        </div>
      </div>

      <div *ngIf="!hasActiveMembership()" class="no-qr-message">
        <i class="fas fa-ban"></i> Aktif üyeliğiniz bulunmadığı için QR kodunuz görüntülenemiyor.
      </div>
    </div>
  </div>
</div>