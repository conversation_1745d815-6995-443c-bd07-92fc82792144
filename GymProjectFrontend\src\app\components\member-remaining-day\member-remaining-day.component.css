/* Member Remaining Day Component Styles */

/* Loading Spinner */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(3px);
}

.spinner-container {
  text-align: center;
}

/* Content Blur */
.content-blur {
  filter: blur(3px);
  pointer-events: none;
}

/* Fade In Animation */
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* Modern Stats Cards */
.modern-stats-card {
  border-radius: var(--border-radius-lg);
  padding: 1.5rem;
  height: 100%;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  margin-bottom: 1rem;
}

.modern-stats-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.modern-stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin-right: 1rem;
  background-color: rgba(255, 255, 255, 0.2);
  flex-shrink: 0;
}

.modern-stats-info {
  flex-grow: 1;
}

.modern-stats-value {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.modern-stats-label {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.modern-stats-subtext {
  font-size: 0.875rem;
  opacity: 0.8;
}

/* Background Gradients */
.bg-warning-gradient {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
  color: white;
}

.bg-danger-gradient {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  color: white;
}

.bg-info-gradient {
  background: linear-gradient(135deg, #0dcaf0 0%, #0097b2 100%);
  color: white;
}

/* Member Avatar */
.member-info {
  display: flex;
  align-items: center;
}

.member-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  margin-right: 0.75rem;
  flex-shrink: 0;
}

/* Status Badge */
.status-badge {
  display: inline-block;
  padding: 0.35rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 50rem;
}

.status-danger {
  background-color: var(--danger-light);
  color: var(--danger);
}

.status-warning {
  background-color: var(--warning-light);
  color: var(--warning);
}

.status-success {
  background-color: var(--success-light);
  color: var(--success);
}

/* Search Box */
.search-box {
  max-width: 300px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* Table Styles */
.modern-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.modern-table th {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.5px;
  padding: 1rem;
  border-bottom: 2px solid var(--border-color);
}

.modern-table td {
  padding: 1rem;
  vertical-align: middle;
  border-bottom: 1px solid var(--border-color);
}

.modern-table tbody tr {
  transition: background-color 0.3s ease;
}

.modern-table tbody tr:hover {
  background-color: var(--primary-light);
}

/* Sort Button */
.sort-button {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--text-secondary);
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.sort-button:hover {
  background-color: var(--primary-light);
  color: var(--primary);
}

.sort-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--primary-light);
}

/* Empty State */
.empty-state {
  padding: 3rem 1rem;
  text-align: center;
  color: var(--text-secondary);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Dark Mode Support */
[data-theme="dark"] .loading-overlay {
  background-color: rgba(18, 18, 18, 0.8);
}

[data-theme="dark"] .modern-table th {
  background-color: var(--bg-tertiary);
}

[data-theme="dark"] .modern-table tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

/* Responsive Adjustments */
@media (max-width: 767.98px) {
  .modern-stats-card {
    margin-bottom: 1rem;
  }
  
  .modern-stats-icon {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
  }
  
  .modern-stats-value {
    font-size: 1.5rem;
  }
  
  .header-actions {
    margin-top: 1rem;
    width: 100%;
  }
  
  .search-box {
    max-width: 100%;
    width: 100%;
  }
  
  .card-header {
    flex-direction: column;
    align-items: flex-start;
  }
}
