-- Migration: Add MemberExerciseProgress Table
-- Date: 2024-12-20
-- Description: Üye egzersiz ilerleme takip sistemi için tablo oluşturma
-- Performance: 10.000+ kullanıcı için optimize edilmiş indexler

-- MemberExerciseProgress tablosu oluştur
CREATE TABLE [dbo].[MemberExerciseProgresses] (
    [MemberExerciseProgressID] INT IDENTITY(1,1) NOT NULL,
    [MemberWorkoutProgramID] INT NOT NULL,
    [WorkoutProgramExerciseID] INT NOT NULL,
    [MemberID] INT NOT NULL,
    [CompanyID] INT NOT NULL,
    [CompletedDate] DATETIME2(7) NOT NULL,
    [CompletedSets] INT NOT NULL,
    [ActualReps] NVARCHAR(50) NULL,
    [Notes] NVARCHAR(500) NULL,
    [CreationDate] DATETIME2(7) NULL,
    [UpdatedDate] DATETIME2(7) NULL,
    [DeletedDate] DATETIME2(7) NULL,
    
    CONSTRAINT [PK_MemberExerciseProgresses] PRIMARY KEY CLUSTERED ([MemberExerciseProgressID] ASC)
);

-- Performance Indexleri (10.000+ kullanıcı için kritik)

-- 1. Üye bazlı sorgular için composite index (en sık kullanılacak)
CREATE NONCLUSTERED INDEX [IX_MemberExerciseProgress_Member_Date] 
ON [dbo].[MemberExerciseProgresses] ([MemberID] ASC, [CompletedDate] DESC)
INCLUDE ([WorkoutProgramExerciseID], [CompletedSets], [ActualReps]);

-- 2. Program bazlı sorgular için index
CREATE NONCLUSTERED INDEX [IX_MemberExerciseProgress_Program] 
ON [dbo].[MemberExerciseProgresses] ([MemberWorkoutProgramID] ASC, [CompletedDate] DESC)
INCLUDE ([MemberID], [WorkoutProgramExerciseID], [CompletedSets]);

-- 3. Multi-tenant için company bazlı index
CREATE NONCLUSTERED INDEX [IX_MemberExerciseProgress_Company] 
ON [dbo].[MemberExerciseProgresses] ([CompanyID] ASC, [CompletedDate] DESC)
INCLUDE ([MemberID], [MemberWorkoutProgramID]);

-- 4. Egzersiz bazlı sorgular için index
CREATE NONCLUSTERED INDEX [IX_MemberExerciseProgress_Exercise] 
ON [dbo].[MemberExerciseProgresses] ([WorkoutProgramExerciseID] ASC, [CompletedDate] DESC)
INCLUDE ([MemberID], [CompletedSets], [ActualReps]);

-- 5. Tarih bazlı sorgular için index (raporlama için)
CREATE NONCLUSTERED INDEX [IX_MemberExerciseProgress_Date_Company] 
ON [dbo].[MemberExerciseProgresses] ([CompletedDate] DESC, [CompanyID] ASC)
INCLUDE ([MemberID], [WorkoutProgramExerciseID], [CompletedSets]);

-- 6. Soft delete için index
CREATE NONCLUSTERED INDEX [IX_MemberExerciseProgress_DeletedDate] 
ON [dbo].[MemberExerciseProgresses] ([DeletedDate] ASC)
WHERE [DeletedDate] IS NULL;

-- Foreign Key Constraints
ALTER TABLE [dbo].[MemberExerciseProgresses] 
ADD CONSTRAINT [FK_MemberExerciseProgress_MemberWorkoutProgram] 
FOREIGN KEY ([MemberWorkoutProgramID]) 
REFERENCES [dbo].[MemberWorkoutPrograms] ([MemberWorkoutProgramID]);

ALTER TABLE [dbo].[MemberExerciseProgresses] 
ADD CONSTRAINT [FK_MemberExerciseProgress_WorkoutProgramExercise] 
FOREIGN KEY ([WorkoutProgramExerciseID]) 
REFERENCES [dbo].[WorkoutProgramExercises] ([WorkoutProgramExerciseID]);

ALTER TABLE [dbo].[MemberExerciseProgresses] 
ADD CONSTRAINT [FK_MemberExerciseProgress_Member] 
FOREIGN KEY ([MemberID]) 
REFERENCES [dbo].[Members] ([MemberID]);

-- Check Constraints
ALTER TABLE [dbo].[MemberExerciseProgresses] 
ADD CONSTRAINT [CK_MemberExerciseProgress_CompletedSets] 
CHECK ([CompletedSets] >= 0 AND [CompletedSets] <= 50);

ALTER TABLE [dbo].[MemberExerciseProgresses] 
ADD CONSTRAINT [CK_MemberExerciseProgress_CompletedDate] 
CHECK ([CompletedDate] >= '2024-01-01' AND [CompletedDate] <= GETDATE());

-- Default Constraints
ALTER TABLE [dbo].[MemberExerciseProgresses] 
ADD CONSTRAINT [DF_MemberExerciseProgress_CreationDate] 
DEFAULT (GETDATE()) FOR [CreationDate];

ALTER TABLE [dbo].[MemberExerciseProgresses] 
ADD CONSTRAINT [DF_MemberExerciseProgress_CompletedDate] 
DEFAULT (GETDATE()) FOR [CompletedDate];

-- Unique Constraint (Aynı gün aynı egzersizi birden fazla tamamlama önleme)
-- Not: Bu constraint opsiyonel - iş kurallarına göre karar verilecek
-- ALTER TABLE [dbo].[MemberExerciseProgresses] 
-- ADD CONSTRAINT [UK_MemberExerciseProgress_Daily] 
-- UNIQUE ([MemberID], [WorkoutProgramExerciseID], [CompletedDate]);

-- Performans için istatistik güncelleme
UPDATE STATISTICS [dbo].[MemberExerciseProgresses];

-- Tablo açıklaması
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Üye egzersiz ilerleme takip tablosu. Her üyenin hangi egzersizi ne zaman tamamladığını tutar. 10.000+ kullanıcı için optimize edilmiştir.', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'MemberExerciseProgresses';
