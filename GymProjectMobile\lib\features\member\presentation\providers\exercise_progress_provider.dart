import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/services/member_exercise_progress_api_service.dart';
import '../../domain/models/exercise_completion_status.dart';
import '../../domain/models/daily_workout_progress.dart';
import '../../domain/models/member_exercise_progress_add.dart';
import '../../../../core/services/auth_service.dart';

/// Egzersiz ilerleme durumu
class ExerciseProgressState {
  final bool isLoading;
  final List<ExerciseCompletionStatus> completionStatuses;
  final DailyWorkoutProgress? dailyProgress;
  final String? error;
  final DateTime lastUpdated;

  ExerciseProgressState({
    this.isLoading = false,
    this.completionStatuses = const [],
    this.dailyProgress,
    this.error,
    DateTime? lastUpdated,
  }) : lastUpdated = lastUpdated ?? DateTime.now();

  ExerciseProgressState copyWith({
    bool? isLoading,
    List<ExerciseCompletionStatus>? completionStatuses,
    DailyWorkoutProgress? dailyProgress,
    String? error,
    DateTime? lastUpdated,
  }) {
    return ExerciseProgressState(
      isLoading: isLoading ?? this.isLoading,
      completionStatuses: completionStatuses ?? this.completionStatuses,
      dailyProgress: dailyProgress ?? this.dailyProgress,
      error: error,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  /// Belirli bir egzersizin tamamlanma durumunu getirir
  ExerciseCompletionStatus? getCompletionStatus(int workoutProgramExerciseId) {
    try {
      return completionStatuses.firstWhere(
        (status) => status.workoutProgramExerciseId == workoutProgramExerciseId,
      );
    } catch (e) {
      return null;
    }
  }

  /// Belirli bir egzersizin tamamlanıp tamamlanmadığını kontrol eder
  bool isExerciseCompleted(int workoutProgramExerciseId) {
    final status = getCompletionStatus(workoutProgramExerciseId);
    return status?.isCompleted ?? false;
  }

  /// Tamamlanan egzersiz sayısı
  int get completedExercisesCount {
    return completionStatuses.where((status) => status.isCompleted).length;
  }

  /// Toplam egzersiz sayısı
  int get totalExercisesCount {
    return completionStatuses.length;
  }

  /// Tamamlama yüzdesi
  double get completionPercentage {
    if (totalExercisesCount == 0) return 0.0;
    return (completedExercisesCount / totalExercisesCount) * 100;
  }
}

/// Egzersiz ilerleme provider
class ExerciseProgressNotifier extends StateNotifier<ExerciseProgressState> {
  final MemberExerciseProgressApiService _apiService;

  ExerciseProgressNotifier(this._apiService) : super(ExerciseProgressState());

  /// Günlük egzersiz tamamlama durumlarını yükler
  Future<void> loadTodayProgress({DateTime? date}) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final targetDate = date ?? DateTime.now();

      // Paralel olarak hem completion status hem de daily progress'i al
      final results = await Future.wait([
        _apiService.getExerciseCompletionStatusByUser(date: targetDate),
        _apiService.getDailyWorkoutProgressByUser(date: targetDate),
      ]);

      final completionResponse = results[0];
      final progressResponse = results[1];

      if (completionResponse.isSuccess && progressResponse.isSuccess) {
        state = state.copyWith(
          isLoading: false,
          completionStatuses: completionResponse.data as List<ExerciseCompletionStatus>? ?? [],
          dailyProgress: progressResponse.data as DailyWorkoutProgress?,
          lastUpdated: DateTime.now(),
        );
      } else {
        final error = completionResponse.isError ? completionResponse.message : progressResponse.message;
        state = state.copyWith(
          isLoading: false,
          error: error,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Bağlantı hatası: $e',
      );
    }
  }

  /// Egzersiz tamamlama işlemi
  Future<bool> completeExercise({
    required int memberWorkoutProgramId,
    required int workoutProgramExerciseId,
    required int completedSets,
    String? actualReps,
    String? notes,
  }) async {
    try {
      // Önce egzersizin zaten tamamlanıp tamamlanmadığını kontrol et
      if (state.isExerciseCompleted(workoutProgramExerciseId)) {
        state = state.copyWith(error: 'Bu egzersiz zaten tamamlanmış');
        return false;
      }

      final progressAdd = MemberExerciseProgressAdd(
        memberWorkoutProgramId: memberWorkoutProgramId,
        workoutProgramExerciseId: workoutProgramExerciseId,
        completedSets: completedSets,
        actualReps: actualReps,
        notes: notes,
      );

      // Validasyon kontrolü
      if (!progressAdd.isValid) {
        state = state.copyWith(error: progressAdd.validationErrors.first);
        return false;
      }

      // Rate limiting ile API çağrısı
      final response = await _apiService.addProgressWithRateLimit(progressAdd);

      if (response.isSuccess) {
        // Başarılı olursa completion status'u güncelle
        final updatedStatuses = state.completionStatuses.map((status) {
          if (status.workoutProgramExerciseId == workoutProgramExerciseId) {
            return status.copyWith(
              isCompleted: true,
              completedDate: DateTime.now(),
              completedSets: completedSets,
              actualReps: actualReps,
              notes: notes,
            );
          }
          return status;
        }).toList();

        state = state.copyWith(
          completionStatuses: updatedStatuses,
          error: null,
          lastUpdated: DateTime.now(),
        );

        // Daily progress'i yeniden yükle
        await _refreshDailyProgress();

        return true;
      } else {
        state = state.copyWith(error: response.message);
        return false;
      }
    } catch (e) {
      state = state.copyWith(error: 'Egzersiz tamamlama hatası: $e');
      return false;
    }
  }

  /// Daily progress'i yeniden yükler
  Future<void> _refreshDailyProgress() async {
    try {
      final response = await _apiService.getDailyWorkoutProgressByUser();
      if (response.isSuccess) {
        state = state.copyWith(dailyProgress: response.data as DailyWorkoutProgress?);
      }
    } catch (e) {
      // Sessizce hata yönet, ana işlemi etkilemesin
    }
  }

  /// Egzersiz tamamlama işlemini geri al (undo)
  Future<bool> uncompleteExercise(int workoutProgramExerciseId) async {
    try {
      // Local state'i güncelle (API'de delete endpoint'i olmadığı için)
      final updatedStatuses = state.completionStatuses.map((status) {
        if (status.workoutProgramExerciseId == workoutProgramExerciseId) {
          return status.copyWith(
            isCompleted: false,
            completedDate: null,
            completedSets: null,
            actualReps: null,
            notes: null,
          );
        }
        return status;
      }).toList();

      state = state.copyWith(
        completionStatuses: updatedStatuses,
        lastUpdated: DateTime.now(),
      );

      return true;
    } catch (e) {
      state = state.copyWith(error: 'Geri alma hatası: $e');
      return false;
    }
  }

  /// Hata mesajını temizler
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// State'i sıfırlar
  void reset() {
    state = ExerciseProgressState();
  }

  /// Belirli bir tarih için progress'i yükler
  Future<void> loadProgressForDate(DateTime date) async {
    await loadTodayProgress(date: date);
  }
}

/// Provider tanımları
final exerciseProgressProvider = StateNotifierProvider<ExerciseProgressNotifier, ExerciseProgressState>((ref) {
  final authService = ref.read(authServiceProvider);
  final apiService = MemberExerciseProgressApiService(authService);
  return ExerciseProgressNotifier(apiService);
});

/// Auth service provider (eğer yoksa)
final authServiceProvider = Provider<AuthService>((ref) {
  return AuthService();
});
