/// Egzersiz ilerleme ekleme modeli
class MemberExerciseProgressAdd {
  final int memberWorkoutProgramId;
  final int workoutProgramExerciseId;
  final int completedSets;
  final String? actualReps;
  final String? notes;

  MemberExerciseProgressAdd({
    required this.memberWorkoutProgramId,
    required this.workoutProgramExerciseId,
    required this.completedSets,
    this.actualReps,
    this.notes,
  });

  factory MemberExerciseProgressAdd.fromJson(Map<String, dynamic> json) {
    return MemberExerciseProgressAdd(
      memberWorkoutProgramId: json['memberWorkoutProgramID'] ?? 0,
      workoutProgramExerciseId: json['workoutProgramExerciseID'] ?? 0,
      completedSets: json['completedSets'] ?? 0,
      actualReps: json['actualReps'],
      notes: json['notes'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'memberWorkoutProgramID': memberWorkoutProgramId,
      'workoutProgramExerciseID': workoutProgramExerciseId,
      'completedSets': completedSets,
      'actualReps': actualReps,
      'notes': notes,
    };
  }

  MemberExerciseProgressAdd copyWith({
    int? memberWorkoutProgramId,
    int? workoutProgramExerciseId,
    int? completedSets,
    String? actualReps,
    String? notes,
  }) {
    return MemberExerciseProgressAdd(
      memberWorkoutProgramId: memberWorkoutProgramId ?? this.memberWorkoutProgramId,
      workoutProgramExerciseId: workoutProgramExerciseId ?? this.workoutProgramExerciseId,
      completedSets: completedSets ?? this.completedSets,
      actualReps: actualReps ?? this.actualReps,
      notes: notes ?? this.notes,
    );
  }

  /// Validasyon kontrolü
  bool get isValid {
    return memberWorkoutProgramId > 0 &&
           workoutProgramExerciseId > 0 &&
           completedSets >= 0 &&
           completedSets <= 50;
  }

  /// Hata mesajları
  List<String> get validationErrors {
    final errors = <String>[];
    
    if (memberWorkoutProgramId <= 0) {
      errors.add('Geçerli bir program seçiniz');
    }
    
    if (workoutProgramExerciseId <= 0) {
      errors.add('Geçerli bir egzersiz seçiniz');
    }
    
    if (completedSets < 0) {
      errors.add('Tamamlanan set sayısı 0\'dan küçük olamaz');
    }
    
    if (completedSets > 50) {
      errors.add('Tamamlanan set sayısı 50\'den büyük olamaz');
    }
    
    if (notes != null && notes!.length > 500) {
      errors.add('Notlar en fazla 500 karakter olabilir');
    }
    
    if (actualReps != null && actualReps!.length > 50) {
      errors.add('Tekrar sayısı en fazla 50 karakter olabilir');
    }
    
    return errors;
  }

  @override
  String toString() {
    return 'MemberExerciseProgressAdd(memberWorkoutProgramId: $memberWorkoutProgramId, workoutProgramExerciseId: $workoutProgramExerciseId, completedSets: $completedSets, actualReps: $actualReps, notes: $notes)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is MemberExerciseProgressAdd &&
        other.memberWorkoutProgramId == memberWorkoutProgramId &&
        other.workoutProgramExerciseId == workoutProgramExerciseId &&
        other.completedSets == completedSets &&
        other.actualReps == actualReps &&
        other.notes == notes;
  }

  @override
  int get hashCode {
    return memberWorkoutProgramId.hashCode ^
        workoutProgramExerciseId.hashCode ^
        completedSets.hashCode ^
        actualReps.hashCode ^
        notes.hashCode;
  }
}
