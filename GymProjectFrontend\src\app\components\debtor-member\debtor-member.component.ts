import { Component, OnInit } from '@angular/core';
import { RemainingDebtService } from '../../services/remaining-debt-service.service'; 
import { RemainingDebtDetail } from '../../models/RemainingDebtDetail';
import { ToastrService } from 'ngx-toastr';
import { MatDialog } from '@angular/material/dialog';
import { DebtPaymentDialogComponent } from '../debt-payment-dialog/debt-payment-dialog.component';

@Component({
    selector: 'app-debtor-member',
    templateUrl: './debtor-member.component.html',
    styleUrls: ['./debtor-member.component.css'],
    standalone: false
})
export class DebtorMemberComponent implements OnInit {
  debtorMembers: RemainingDebtDetail[] = [];
  filteredDebtorMembers: RemainingDebtDetail[] = [];
  searchText: string = '';
  isLoading: boolean = false;
  sortColumn: string = 'lastUpdateDate'; // Default sort column
  sortDirection: 'asc' | 'desc' = 'desc'; // Default sort direction

  constructor(
    private remainingDebtService: RemainingDebtService,
    private toastrService: ToastrService,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.getDebtorMembers();
  }

  getDebtorMembers() {
    this.isLoading = true;
    this.remainingDebtService.getRemainingDebtDetails().subscribe(
      (response) => {
        this.debtorMembers = response.data;
        // .reverse() kaldırıldı, sıralama filterDebtorMembers içinde yapılacak
        this.filterDebtorMembers();
        this.isLoading = false;
      },
      (error) => {
        this.toastrService.error('Borçlu üyeler yüklenirken bir hata oluştu.', 'Hata');
        this.isLoading = false;
      }
    );
  }

  openPaymentDialog(debt: RemainingDebtDetail) {
    const dialogRef = this.dialog.open(DebtPaymentDialogComponent, {
      width: '400px',
      data: debt
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.isLoading = true;
        this.remainingDebtService.addDebtPayment(result).subscribe(
          (response) => {
            this.toastrService.success('Ödeme başarıyla kaydedildi', 'Başarılı');
            this.getDebtorMembers();
          },
          (error) => {
            this.toastrService.error('Ödeme kaydedilirken bir hata oluştu', 'Hata');
            this.isLoading = false;
          }
        );
        this.sortDebtorMembers(); // Filtrelemeden sonra sırala
      }
    });
  }
  
  // Filter debtor members based on search text
  filterDebtorMembers() {
    // 1. Filtreleme
    if (!this.searchText) {
      // Arama metni yoksa tüm üyeleri al
      this.filteredDebtorMembers = [...this.debtorMembers];
    } else {
      // Arama metni varsa filtrele
      const searchLower = this.searchText.toLowerCase();
      this.filteredDebtorMembers = this.debtorMembers.filter(debt =>
        debt.memberName.toLowerCase().includes(searchLower) ||
        debt.phoneNumber.toLowerCase().includes(searchLower)
      );
    }
    
    // 2. Sıralama (Filtreleme sonrası her zaman çalışır)
    this.sortDebtorMembers();
  }
  
  // Get total number of debtor members
  getTotalDebtorCount(): number {
    return this.debtorMembers.length;
  }
  
  // Get total original debt amount
  getTotalOriginalDebt(): number {
    return this.debtorMembers.reduce((total, debt) => total + debt.originalAmount, 0);
  }
  
  // Get total remaining debt amount
  getTotalRemainingDebt(): number {
    return this.debtorMembers.reduce((total, debt) => total + debt.remainingAmount, 0);
  }
  
  // Get payment percentage for progress bar
  getPaymentPercentage(debt: RemainingDebtDetail): number {
    if (debt.originalAmount === 0) return 0;
    const paid = debt.originalAmount - debt.remainingAmount;
    const percentage = (paid / debt.originalAmount) * 100;
    return Math.round(percentage);
  }
  
  // Get progress bar class based on payment percentage
  getProgressBarClass(debt: RemainingDebtDetail): string {
    const percentage = this.getPaymentPercentage(debt);
    
    if (percentage >= 75) return 'bg-success';
    if (percentage >= 50) return 'bg-info';
    if (percentage >= 25) return 'bg-warning';
    return 'bg-danger';
  }
  
  // Get initials from name
  getInitials(name: string): string {
    if (!name) return '';
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  }
  
  // Generate avatar background color based on name
  getAvatarColor(name: string): string {
    if (!name) return '#dc3545';
    
    const colors = [
      '#dc3545', '#fd7e14', '#ffc107', '#20c997', 
      '#0dcaf0', '#0d6efd', '#6610f2', '#6f42c1'
    ];
    
    // Simple hash function to get consistent color for the same name
    let hash = 0;
    for (let i = 0; i < name.length; i++) {
      hash = name.charCodeAt(i) + ((hash << 5) - hash);
    }
    
    return colors[Math.abs(hash) % colors.length];
  }

  // Toggle sort direction or change sort column
  toggleSort(column: string) {
    if (this.sortColumn === column) {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortColumn = column;
      this.sortDirection = 'desc'; // Default to descending for new column
    }
    this.sortDebtorMembers();
  }

  // Sort the filtered debtor members array
  sortDebtorMembers() {
    if (!this.filteredDebtorMembers) return;

    this.filteredDebtorMembers.sort((a, b) => {
      let valA: any;
      let valB: any;

      // Handle specific column sorting logic if needed
      if (this.sortColumn === 'lastUpdateDate') {
        valA = new Date(a.lastUpdateDate).getTime();
        valB = new Date(b.lastUpdateDate).getTime();
      } else {
        // Default string/number comparison (add more specific cases if needed)
        valA = (a as any)[this.sortColumn];
        valB = (b as any)[this.sortColumn];
      }

      // Basic comparison logic (can be expanded for different types)
      let comparison = 0;
      if (valA > valB) {
        comparison = 1;
      } else if (valA < valB) {
        comparison = -1;
      }

      return this.sortDirection === 'desc' ? comparison * -1 : comparison;
    });
  }
}
