/* Expense Dialog specific styles can be added here if needed */
.expense-form {
  padding-top: 10px; /* Add some padding to the top of the form */
}

/* Ensure spinner aligns well within the button */
.mat-mdc-button-persistent-ripple {
    display: flex;
    align-items: center;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
    border-width: .2em;
    margin-right: 0.5rem; /* Add space between spinner and text */
}

/* Dark mode styles moved to global styles.css */