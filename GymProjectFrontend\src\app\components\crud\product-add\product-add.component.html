<div class="container mt-4">
    <h2><PERSON><PERSON></h2>
    <form [formGroup]="productForm" (ngSubmit)="addProduct()">
      <div class="form-group">
        <label for="name"><PERSON><PERSON><PERSON><PERSON></label>
        <input type="text" id="name" formControlName="name" class="form-control" required>
      </div>
      <div class="form-group">
        <label for="price">Fiyat</label>
        <input type="number" id="price" formControlName="price" class="form-control" required min="0" step="0.01">
      </div>
      <div class="form-group form-check">
      </div>
      <button type="submit" class="btn btn-primary" [disabled]="!productForm.valid || isSubmitting">
        {{ isSubmitting ? 'Ekleniyor...' : '<PERSON><PERSON><PERSON><PERSON>' }}
      </button>
    </form>
  </div>