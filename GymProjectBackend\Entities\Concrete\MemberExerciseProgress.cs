using Core.Entities;
using System;
using System.ComponentModel.DataAnnotations;

namespace Entities.Concrete
{
    /// <summary>
    /// Üye egzersiz ilerleme takip tablosu
    /// Her üyenin hangi egzersizi ne zaman tamamladığını tutar
    /// </summary>
    public class MemberExerciseProgress : ICompanyEntity
    {
        [Key]
        public int MemberExerciseProgressID { get; set; }
        
        /// <summary>
        /// Hangi program atamasına ait
        /// </summary>
        public int MemberWorkoutProgramID { get; set; }
        
        /// <summary>
        /// Hangi egzersiz tamamlandı
        /// </summary>
        public int WorkoutProgramExerciseID { get; set; }
        
        /// <summary>
        /// Hangi üye tamamladı
        /// </summary>
        public int MemberID { get; set; }
        
        /// <summary>
        /// Multi-tenant için company ID
        /// </summary>
        public int CompanyID { get; set; }
        
        /// <summary>
        /// Egzersizin tamamlandığı tarih
        /// </summary>
        public DateTime CompletedDate { get; set; }
        
        /// <summary>
        /// Tamamlanan set sayısı
        /// </summary>
        public int CompletedSets { get; set; }
        
        /// <summary>
        /// Gerçekte yapılan tekrar sayısı (12, 15, MAX vb.)
        /// </summary>
        public string? ActualReps { get; set; }
        
        /// <summary>
        /// Üyenin egzersiz hakkındaki notları
        /// </summary>
        public string? Notes { get; set; }
        
        /// <summary>
        /// Kayıt oluşturulma tarihi
        /// </summary>
        public DateTime? CreationDate { get; set; }
        
        /// <summary>
        /// Son güncellenme tarihi
        /// </summary>
        public DateTime? UpdatedDate { get; set; }
        
        /// <summary>
        /// Silinme tarihi (soft delete)
        /// </summary>
        public DateTime? DeletedDate { get; set; }
    }
}
