import { Component, isStandalone, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';
import { LicensePackageService } from '../../services/license-package.service';
import { DialogService } from '../../services/dialog.service';
import { LicensePackage } from '../../models/licensePackage';
import { LicensePackageAddEditComponent } from '../crud/license-package-add-edit/license-package-add-edit.component';

@Component({
  selector: 'app-license-packages-list',
  templateUrl: './license-packages-list.component.html',
  styleUrls: ['./license-packages-list.component.css'],
  standalone:false
})
export class LicensePackagesListComponent implements OnInit {
  licensePackages: LicensePackage[] = [];
  isLoading = false;
  displayedColumns: string[] = ['name', 'description', 'role', 'durationDays', 'price', 'actions'];

  constructor(
    private licensePackageService: LicensePackageService,
    private dialogService: DialogService,
    private dialog: MatDialog,
    private toastr: ToastrService
  ) {}

  ngOnInit(): void {
    this.loadLicensePackages();
  }

  loadLicensePackages(): void {
    this.isLoading = true;
    this.licensePackageService.getAll().subscribe({
      next: (response) => {
        this.licensePackages = response.data;
        this.isLoading = false;
      },
      error: (error) => {
        this.toastr.error('Lisans paketleri yüklenirken bir hata oluştu', 'Hata');
        this.isLoading = false;
      }
    });
  }

  openAddDialog(): void {
    const dialogRef = this.dialog.open(LicensePackageAddEditComponent, {
      width: '600px',
      data: { isEdit: false }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadLicensePackages();
      }
    });
  }

  openEditDialog(licensePackage: LicensePackage): void {
    const dialogRef = this.dialog.open(LicensePackageAddEditComponent, {
      width: '600px',
      data: { isEdit: true, licensePackage: licensePackage }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadLicensePackages();
      }
    });
  }

  deleteLicensePackage(id: number, name: string): void {
    this.dialogService.confirmDelete(name).subscribe((confirmed) => {
      if (confirmed) {
        this.licensePackageService.delete(id).subscribe({
          next: (response) => {
            this.toastr.success(response.message, 'Başarılı');
            this.loadLicensePackages();
          },
          error: (error) => {
            this.toastr.error('Lisans paketi silinirken bir hata oluştu', 'Hata');
          }
        });
      }
    });
  }
}
