/// API Constants for GymKod Pro Mobile
class ApiConstants {
  // Base URL - Production'da değiştirilecek
  static const String baseUrl = 'https://localhost:7001';
  
  // API Endpoints
  static const String loginEndpoint = '/api/Auth/login';
  static const String registerEndpoint = '/api/Auth/register';
  static const String refreshTokenEndpoint = '/api/Auth/refreshtoken';
  
  // Member endpoints
  static const String memberProfileEndpoint = '/api/Member/getprofile';
  static const String memberWorkoutProgramsEndpoint = '/api/MemberWorkoutProgram/getactiveprogramsbyuser';
  static const String memberWorkoutProgramDetailEndpoint = '/api/MemberWorkoutProgram/getprogramdetailbyuser';
  
  // Exercise progress endpoints
  static const String exerciseProgressEndpoint = '/api/MemberExerciseProgress';
  
  // Timeout values
  static const Duration connectTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const Duration sendTimeout = Duration(seconds: 30);
  
  // Headers
  static const Map<String, String> defaultHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };
  
  // Error messages
  static const String networkErrorMessage = 'İnternet bağlantınızı kontrol edin';
  static const String serverErrorMessage = 'Sunucu hatası oluştu';
  static const String timeoutErrorMessage = 'İstek zaman aşımına uğradı';
  static const String unauthorizedErrorMessage = 'Oturum süresi dolmuş';
  
  // Status codes
  static const int successCode = 200;
  static const int createdCode = 201;
  static const int badRequestCode = 400;
  static const int unauthorizedCode = 401;
  static const int forbiddenCode = 403;
  static const int notFoundCode = 404;
  static const int serverErrorCode = 500;
}
