.last-membership-info {
    margin-top: 5px;
    font-size: 0.85em;
    color: #fc4c4c;
  }

  .form-group {
    margin-bottom: 0;
  }

  .mb-3 {
    margin-bottom: 1rem !important;
  }

  /* Zorunlu alanlar için hata durumu */
  .form-control.ng-invalid.ng-touched {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
  }

  /* <PERSON>itreşim animasyonu için sınıf */
  .shake-animation {
    animation: shake 0.6s cubic-bezier(.36,.07,.19,.97) both;
    transform: translate3d(0, 0, 0);
    backface-visibility: hidden;
    perspective: 1000px;
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.5) !important;
  }

  @keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-6px); }
    20%, 40%, 60%, 80% { transform: translateX(6px); }
  }