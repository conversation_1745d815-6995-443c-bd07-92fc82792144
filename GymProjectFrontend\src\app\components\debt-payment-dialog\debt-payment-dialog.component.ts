import { Component, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { RemainingDebtDetail } from '../../models/RemainingDebtDetail'; 

@Component({
    selector: 'app-debt-payment-dialog',
    template: `
    <div class="modern-dialog zoom-in">
      <div class="modern-card-header">
        <h2>Ödeme Al</h2>
        <button class="modern-btn-icon" (click)="onNoClick()">
          <i class="fas fa-times"></i>
        </button>
      </div>
      
      <div class="modern-card-body">
        <div class="dialog-icon">
          <i class="fas fa-money-bill-wave"></i>
        </div>
        
        <div class="debt-summary">
          <div class="debt-info">
            <div class="debt-label">Toplam Borç</div>
            <div class="debt-value">{{ data.originalAmount | currency:'TRY':'symbol-narrow':'1.2-2' }}</div>
          </div>
          <div class="progress-container">
            <div class="progress-bar" [style.width.%]="getPaymentPercentage()"></div>
          </div>
          <div class="debt-info">
            <div class="debt-label">Kalan Borç</div>
            <div class="debt-value remaining">{{ data.remainingAmount | currency:'TRY':'symbol-narrow':'1.2-2' }}</div>
          </div>
        </div>
        
        <form [formGroup]="paymentForm" class="payment-form">
          <div class="modern-form-group">
            <label class="modern-form-label">Ödenecek Tutar</label>
            <div class="input-group">
              <div class="input-group-text">
                <i class="fas fa-money-bill"></i>
              </div>
              <input 
                type="number" 
                class="modern-form-control" 
                formControlName="paidAmount" 
                [max]="data.remainingAmount" 
                [min]="1"
                placeholder="0.00"
              >
            </div>
            <div *ngIf="paymentForm.get('paidAmount')?.invalid && paymentForm.get('paidAmount')?.touched" class="error-message">
              <div *ngIf="paymentForm.get('paidAmount')?.hasError('required')">
                Tutar gereklidir
              </div>
              <div *ngIf="paymentForm.get('paidAmount')?.hasError('max')">
                Tutar kalan borçtan büyük olamaz
              </div>
              <div *ngIf="paymentForm.get('paidAmount')?.hasError('min')">
                Tutar 1'den küçük olamaz
              </div>
            </div>
          </div>
          
          <div class="modern-form-group">
            <label class="modern-form-label">Ödeme Yöntemi</label>
            <div class="payment-methods">
              <div 
                *ngFor="let method of paymentMethods" 
                class="payment-method-option" 
                [class.selected]="paymentForm.get('paymentMethod')?.value === method.value"
                (click)="selectPaymentMethod(method.value)"
              >
                <div class="payment-method-icon">
                  <i class="fas" [ngClass]="method.icon"></i>
                </div>
                <div class="payment-method-label">{{method.label}}</div>
              </div>
            </div>
            <div *ngIf="paymentForm.get('paymentMethod')?.invalid && paymentForm.get('paymentMethod')?.touched" class="error-message">
              Ödeme yöntemi seçilmelidir
            </div>
          </div>
          
          <div class="payment-summary" *ngIf="paymentForm.get('paidAmount')?.valid && paymentForm.get('paidAmount')?.value">
            <div class="summary-item">
              <div class="summary-label">Ödeme Tutarı:</div>
              <div class="summary-value">{{ paymentForm.get('paidAmount')?.value | currency:'TRY':'symbol-narrow':'1.2-2' }}</div>
            </div>
            <div class="summary-item">
              <div class="summary-label">Ödeme Sonrası Kalan:</div>
              <div class="summary-value">{{ getRemainingAfterPayment() | currency:'TRY':'symbol-narrow':'1.2-2' }}</div>
            </div>
          </div>
        </form>
      </div>
      
      <div class="modern-card-footer">
        <button class="modern-btn modern-btn-outline-secondary" (click)="onNoClick()">
          <i class="fas fa-times modern-btn-icon"></i> İptal
        </button>
        <button 
          class="modern-btn modern-btn-success" 
          [disabled]="!paymentForm.valid"
          [title]="getButtonTooltip()"
          (click)="dialogRef.close(getPaymentData())">
          <i class="fas fa-check modern-btn-icon"></i> Ödeme Al
        </button>
      </div>
    </div>
  `,
    styles: [`
    .modern-dialog {
      min-width: 320px;
      max-width: 100%;
      overflow: hidden;
      border-radius: var(--border-radius-lg);
      box-shadow: var(--shadow-md);
      background-color: var(--bg-primary);
      color: var(--text-primary);
    }
    
    .modern-card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: var(--spacing-md) var(--spacing-lg);
      border-bottom: 1px solid var(--border-color);
      background-color: var(--success-light);
      color: var(--text-primary);
    }
    
    .modern-card-header h2 {
      margin: 0;
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--text-primary);
    }
    
    .modern-card-body {
      padding: var(--spacing-lg);
      background-color: var(--bg-primary);
      color: var(--text-primary);
    }
    
    .dialog-icon {
      width: 64px;
      height: 64px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto var(--spacing-md);
      font-size: 1.75rem;
      background-color: var(--success-light);
      color: var(--success);
    }
    
    .debt-summary {
      background-color: var(--bg-secondary);
      border-radius: var(--border-radius-md);
      padding: var(--spacing-md);
      margin-bottom: var(--spacing-md);
      color: var(--text-primary);
    }
    
    .debt-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-xs);
    }
    
    .debt-label {
      font-weight: 500;
      color: var(--text-secondary);
    }
    
    .debt-value {
      font-weight: 600;
      color: var(--text-primary);
    }
    
    .debt-value.remaining {
      color: var(--danger);
    }
    
    .progress-container {
      height: 8px;
      background-color: var(--bg-tertiary);
      border-radius: 4px;
      margin: var(--spacing-sm) 0;
      overflow: hidden;
    }
    
    .progress-bar {
      height: 100%;
      background-color: var(--success);
      border-radius: 4px;
    }
    
    .payment-form {
      margin-top: var(--spacing-md);
    }
    
    .modern-form-group {
      margin-bottom: var(--spacing-md);
    }
    
    .modern-form-label {
      display: block;
      margin-bottom: var(--spacing-xs);
      font-weight: 500;
    }
    
    .input-group {
      display: flex;
    }
    
    .input-group-text {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      background-color: var(--success-light);
      color: var(--success);
      border: 1px solid var(--border-color);
      border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);
    }
    
    .modern-form-control {
      flex: 1;
      padding: 0.5rem 0.75rem;
      border: 1px solid var(--border-color);
      border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
      background-color: var(--input-bg);
      color: var(--input-text);
      transition: border-color 0.3s ease, box-shadow 0.3s ease;
    }
    
    .modern-form-control:focus {
      border-color: var(--success);
      outline: none;
      box-shadow: 0 0 0 0.2rem var(--success-light);
    }
    
    .error-message {
      color: var(--danger);
      font-size: 0.875rem;
      margin-top: 0.25rem;
    }
    
    .payment-methods {
      display: flex;
      flex-wrap: wrap;
      gap: var(--spacing-sm);
      margin-bottom: var(--spacing-sm);
    }
    
    .payment-method-option {
      flex: 1;
      min-width: 80px;
      padding: var(--spacing-sm);
      border: 1px solid var(--border-color);
      border-radius: var(--border-radius-md);
      background-color: var(--bg-secondary);
      color: var(--text-primary);
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    
    .payment-method-option:hover {
      border-color: var(--success);
      background-color: var(--success-light);
    }
    
    .payment-method-option.selected {
      border-color: var(--success);
      background-color: var(--success-light);
    }
    
    [data-theme="dark"] .payment-method-option.selected {
      border-color: var(--success);
      background-color: rgba(76, 175, 80, 0.2);
    }
    
    .payment-method-icon {
      font-size: 1.25rem;
      color: var(--text-secondary);
      margin-bottom: var(--spacing-xs);
    }
    
    [data-theme="dark"] .payment-method-icon {
      color: var(--text-primary);
    }
    
    .payment-method-option.selected .payment-method-icon {
      color: var(--success);
    }
    
    .payment-method-label {
      font-size: 0.875rem;
      text-align: center;
    }
    
    .payment-summary {
      background-color: var(--bg-secondary);
      border-radius: var(--border-radius-md);
      padding: var(--spacing-md);
      margin-top: var(--spacing-md);
      color: var(--text-primary);
    }
    
    .summary-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: var(--spacing-xs);
    }
    
    .summary-item:last-child {
      margin-bottom: 0;
      padding-top: var(--spacing-xs);
      border-top: 1px dashed var(--border-color);
    }
    
    /* Dark mode specific overrides */
    [data-theme="dark"] .modern-dialog {
      background-color: var(--bg-secondary);
    }
    
    [data-theme="dark"] .modern-card-body {
      background-color: var(--bg-secondary);
    }
    
    [data-theme="dark"] .debt-summary {
      background-color: var(--bg-tertiary);
    }
    
    [data-theme="dark"] .payment-summary {
      background-color: var(--bg-tertiary);
    }
    
    .summary-label {
      font-weight: 500;
    }
    
    .summary-value {
      font-weight: 600;
    }
    
    .modern-card-footer {
      padding: var(--spacing-md) var(--spacing-lg);
      border-top: 1px solid var(--border-color);
      background-color: var(--bg-secondary);
      display: flex;
      justify-content: flex-end;
      gap: var(--spacing-sm);
      color: var(--text-primary);
    }
    
    .modern-btn-icon {
      margin-right: var(--spacing-xs);
    }
    
    @media screen and (max-width: 480px) {
      .modern-dialog {
        min-width: 280px;
      }
      
      .modern-card-header {
        padding: var(--spacing-sm) var(--spacing-md);
      }
      
      .modern-card-body {
        padding: var(--spacing-md);
      }
      
      .modern-card-footer {
        padding: var(--spacing-sm) var(--spacing-md);
      }
      
      .dialog-icon {
        width: 48px;
        height: 48px;
        font-size: 1.25rem;
      }
      
      .payment-methods {
        flex-direction: column;
      }
      
      .payment-method-option {
        flex-direction: row;
        justify-content: flex-start;
      }
      
      .payment-method-icon {
        margin-right: var(--spacing-sm);
        margin-bottom: 0;
      }
    }
    
    @keyframes zoomIn {
      from { opacity: 0; transform: scale(0.9); }
      to { opacity: 1; transform: scale(1); }
    }
  `],
    standalone: false
})
export class DebtPaymentDialogComponent {
  paymentForm: FormGroup;
  paymentMethods = [
    { value: 'Nakit', label: 'Nakit', icon: 'fa-money-bill-alt' },
    { value: 'Kredi Kartı', label: 'Kredi Kartı', icon: 'fa-credit-card' },
    { value: 'Havale - EFT', label: 'Havale - EFT', icon: 'fa-university' }
  ];

  constructor(
    public dialogRef: MatDialogRef<DebtPaymentDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: RemainingDebtDetail,
    private fb: FormBuilder
  ) {
    this.paymentForm = this.fb.group({
      paidAmount: ['', [Validators.required, Validators.min(1), Validators.max(data.remainingAmount)]],
      paymentMethod: ['', Validators.required]
    });
  }

  onNoClick(): void {
    this.dialogRef.close();
  }

  getPaymentData() {
    if (this.paymentForm.valid) {
      return {
        remainingDebtID: this.data.remainingDebtID,
        paidAmount: this.paymentForm.get('paidAmount')?.value,
        paymentMethod: this.paymentForm.get('paymentMethod')?.value
      };
    }
    return null;
  }
  
  selectPaymentMethod(method: string) {
    this.paymentForm.get('paymentMethod')?.setValue(method);
  }
  
  getPaymentPercentage(): number {
    const paid = this.data.originalAmount - this.data.remainingAmount;
    return (paid / this.data.originalAmount) * 100;
  }
  
  getRemainingAfterPayment(): number {
    const paidAmount = this.paymentForm.get('paidAmount')?.value || 0;
    return Math.max(0, this.data.remainingAmount - paidAmount);
  }
  
  getButtonTooltip(): string {
    if (this.paymentForm.get('paidAmount')?.valid && 
        this.paymentForm.get('paidAmount')?.value > 0 && 
        !this.paymentForm.get('paymentMethod')?.value) {
      return 'Lütfen bir ödeme yöntemi seçiniz';
    }
    
    // Check if amount is invalid
    if (this.paymentForm.get('paidAmount')?.invalid) {
      if (this.paymentForm.get('paidAmount')?.hasError('required')) {
        return 'Lütfen bir tutar giriniz';
      }
      if (this.paymentForm.get('paidAmount')?.hasError('min')) {
        return 'Tutar 1\'den küçük olamaz';
      }
      if (this.paymentForm.get('paidAmount')?.hasError('max')) {
        return 'Tutar kalan borçtan büyük olamaz';
      }
    }
    
    return '';
  }
}