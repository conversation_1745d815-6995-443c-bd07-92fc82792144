using Core.DataAccess.EntityFramework;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DataAccess.Concrete.EntityFramework
{
    /// <summary>
    /// Üye egzersiz ilerleme takip DAL implementasyonu
    /// Performance optimized for 10.000+ users
    /// </summary>
    public class EfMemberExerciseProgressDal : EfEntityRepositoryBase<MemberExerciseProgress, GymContext>, IMemberExerciseProgressDal
    {
        public List<MemberExerciseProgressListDto> GetCompanyProgressList(int companyId, int page = 1, int pageSize = 50)
        {
            using (GymContext context = new GymContext())
            {
                var result = from mep in context.MemberExerciseProgresses
                             join m in context.Members on mep.MemberID equals m.MemberID
                             join wpe in context.WorkoutProgramExercises on mep.WorkoutProgramExerciseID equals wpe.WorkoutProgramExerciseID
                             where mep.CompanyID == companyId && mep.DeletedDate == null
                             orderby mep.CompletedDate descending
                             select new MemberExerciseProgressListDto
                             {
                                 MemberExerciseProgressID = mep.MemberExerciseProgressID,
                                 MemberID = mep.MemberID,
                                 MemberName = m.Name,
                                 ExerciseName = "Egzersiz", // Basitleştirilmiş versiyon
                                 CategoryName = "Kategori",
                                 CompletedDate = mep.CompletedDate,
                                 CompletedSets = mep.CompletedSets,
                                 ActualReps = mep.ActualReps
                             };

                return result.Skip((page - 1) * pageSize).Take(pageSize).ToList();
            }
        }

        public List<MemberExerciseProgressDto> GetMemberProgressList(int memberId, int page = 1, int pageSize = 50)
        {
            using (GymContext context = new GymContext())
            {
                var result = from mep in context.MemberExerciseProgresses
                             join m in context.Members on mep.MemberID equals m.MemberID
                             join wpe in context.WorkoutProgramExercises on mep.WorkoutProgramExerciseID equals wpe.WorkoutProgramExerciseID
                             where mep.MemberID == memberId && mep.DeletedDate == null
                             orderby mep.CompletedDate descending
                             select new MemberExerciseProgressDto
                             {
                                 MemberExerciseProgressID = mep.MemberExerciseProgressID,
                                 MemberWorkoutProgramID = mep.MemberWorkoutProgramID,
                                 WorkoutProgramExerciseID = mep.WorkoutProgramExerciseID,
                                 MemberID = mep.MemberID,
                                 MemberName = m.Name,
                                 ExerciseName = "Egzersiz", // Basitleştirilmiş versiyon
                                 CategoryName = "Kategori",
                                 CompletedDate = mep.CompletedDate,
                                 CompletedSets = mep.CompletedSets,
                                 ActualReps = mep.ActualReps,
                                 Notes = mep.Notes,
                                 CreationDate = mep.CreationDate
                             };

                return result.Skip((page - 1) * pageSize).Take(pageSize).ToList();
            }
        }

        public List<MemberExerciseProgressDto> GetMemberProgressByDate(int memberId, DateTime date)
        {
            using (GymContext context = new GymContext())
            {
                var dateOnly = date.Date;
                var nextDay = dateOnly.AddDays(1);

                var result = from mep in context.MemberExerciseProgresses
                             join m in context.Members on mep.MemberID equals m.MemberID
                             join wpe in context.WorkoutProgramExercises on mep.WorkoutProgramExerciseID equals wpe.WorkoutProgramExerciseID
                             where mep.MemberID == memberId
                                   && mep.CompletedDate >= dateOnly
                                   && mep.CompletedDate < nextDay
                                   && mep.DeletedDate == null
                             orderby mep.CreationDate descending
                             select new MemberExerciseProgressDto
                             {
                                 MemberExerciseProgressID = mep.MemberExerciseProgressID,
                                 MemberWorkoutProgramID = mep.MemberWorkoutProgramID,
                                 WorkoutProgramExerciseID = mep.WorkoutProgramExerciseID,
                                 MemberID = mep.MemberID,
                                 MemberName = m.Name,
                                 ExerciseName = "Egzersiz", // Basitleştirilmiş versiyon
                                 CategoryName = "Kategori",
                                 CompletedDate = mep.CompletedDate,
                                 CompletedSets = mep.CompletedSets,
                                 ActualReps = mep.ActualReps,
                                 Notes = mep.Notes,
                                 CreationDate = mep.CreationDate
                             };

                return result.ToList();
            }
        }

        public List<MemberExerciseProgressDto> GetMemberProgressByProgram(int memberId, int memberWorkoutProgramId)
        {
            using (GymContext context = new GymContext())
            {
                var result = from mep in context.MemberExerciseProgresses
                             join m in context.Members on mep.MemberID equals m.MemberID
                             join wpe in context.WorkoutProgramExercises on mep.WorkoutProgramExerciseID equals wpe.WorkoutProgramExerciseID
                             where mep.MemberID == memberId
                                   && mep.MemberWorkoutProgramID == memberWorkoutProgramId
                                   && mep.DeletedDate == null
                             orderby mep.CompletedDate descending
                             select new MemberExerciseProgressDto
                             {
                                 MemberExerciseProgressID = mep.MemberExerciseProgressID,
                                 MemberWorkoutProgramID = mep.MemberWorkoutProgramID,
                                 WorkoutProgramExerciseID = mep.WorkoutProgramExerciseID,
                                 MemberID = mep.MemberID,
                                 MemberName = m.Name,
                                 ExerciseName = "Egzersiz", // Basitleştirilmiş versiyon
                                 CategoryName = "Kategori",
                                 CompletedDate = mep.CompletedDate,
                                 CompletedSets = mep.CompletedSets,
                                 ActualReps = mep.ActualReps,
                                 Notes = mep.Notes,
                                 CreationDate = mep.CreationDate
                             };

                return result.ToList();
            }
        }

        public MemberExerciseProgressStatsDto GetMemberProgressStats(int memberId)
        {
            using (GymContext context = new GymContext())
            {
                var member = context.Members.FirstOrDefault(m => m.MemberID == memberId);
                if (member == null) return null;

                var now = DateTime.Now;
                var weekStart = now.AddDays(-(int)now.DayOfWeek);
                var monthStart = new DateTime(now.Year, now.Month, 1);

                var stats = from mep in context.MemberExerciseProgresses
                           where mep.MemberID == memberId && mep.DeletedDate == null
                           group mep by mep.MemberID into g
                           select new MemberExerciseProgressStatsDto
                           {
                               MemberID = memberId,
                               MemberName = member.Name,
                               TotalCompletedExercises = g.Count(),
                               TotalCompletedSets = g.Sum(x => x.CompletedSets),
                               LastWorkoutDate = g.Max(x => x.CompletedDate),
                               WeeklyCompletedExercises = g.Count(x => x.CompletedDate >= weekStart),
                               MonthlyCompletedExercises = g.Count(x => x.CompletedDate >= monthStart),
                               CompletionPercentage = 0 // Bu hesaplama ayrı bir metodda yapılacak
                           };

                return stats.FirstOrDefault() ?? new MemberExerciseProgressStatsDto
                {
                    MemberID = memberId,
                    MemberName = member.Name,
                    TotalCompletedExercises = 0,
                    TotalCompletedSets = 0,
                    LastWorkoutDate = null,
                    WeeklyCompletedExercises = 0,
                    MonthlyCompletedExercises = 0,
                    CompletionPercentage = 0
                };
            }
        }

        public ExerciseCompletionStatusDto GetExerciseCompletionStatus(int memberId, int workoutProgramExerciseId, DateTime date)
        {
            using (GymContext context = new GymContext())
            {
                var dateOnly = date.Date;
                var nextDay = dateOnly.AddDays(1);

                var progress = context.MemberExerciseProgresses
                    .Where(mep => mep.MemberID == memberId
                                  && mep.WorkoutProgramExerciseID == workoutProgramExerciseId
                                  && mep.CompletedDate >= dateOnly
                                  && mep.CompletedDate < nextDay
                                  && mep.DeletedDate == null)
                    .OrderByDescending(mep => mep.CreationDate)
                    .FirstOrDefault();

                return new ExerciseCompletionStatusDto
                {
                    WorkoutProgramExerciseID = workoutProgramExerciseId,
                    IsCompleted = progress != null,
                    CompletedDate = progress?.CompletedDate,
                    CompletedSets = progress?.CompletedSets,
                    ActualReps = progress?.ActualReps,
                    Notes = progress?.Notes
                };
            }
        }

        public DailyWorkoutProgressDto GetDailyWorkoutProgress(int memberId, DateTime date)
        {
            using (GymContext context = new GymContext())
            {
                var dateOnly = date.Date;
                var nextDay = dateOnly.AddDays(1);

                // Üyenin aktif programlarını al
                var activePrograms = context.MemberWorkoutPrograms
                    .Where(mwp => mwp.MemberID == memberId && mwp.IsActive)
                    .Select(mwp => mwp.MemberWorkoutProgramID)
                    .ToList();

                if (!activePrograms.Any())
                {
                    return new DailyWorkoutProgressDto
                    {
                        WorkoutDate = date,
                        TotalExercises = 0,
                        CompletedExercises = 0,
                        CompletionPercentage = 0,
                        TotalSets = 0,
                        CompletedSets = 0
                    };
                }

                // Toplam egzersiz sayısını hesapla
                var totalExercises = (from mwp in context.MemberWorkoutPrograms
                                     join wpt in context.WorkoutProgramTemplates on mwp.WorkoutProgramTemplateID equals wpt.WorkoutProgramTemplateID
                                     join wpd in context.WorkoutProgramDays on wpt.WorkoutProgramTemplateID equals wpd.WorkoutProgramTemplateID
                                     join wpe in context.WorkoutProgramExercises on wpd.WorkoutProgramDayID equals wpe.WorkoutProgramDayID
                                     where activePrograms.Contains(mwp.MemberWorkoutProgramID)
                                     select wpe).Count();

                var totalSets = (from mwp in context.MemberWorkoutPrograms
                                join wpt in context.WorkoutProgramTemplates on mwp.WorkoutProgramTemplateID equals wpt.WorkoutProgramTemplateID
                                join wpd in context.WorkoutProgramDays on wpt.WorkoutProgramTemplateID equals wpd.WorkoutProgramTemplateID
                                join wpe in context.WorkoutProgramExercises on wpd.WorkoutProgramDayID equals wpe.WorkoutProgramDayID
                                where activePrograms.Contains(mwp.MemberWorkoutProgramID)
                                select wpe.Sets).Sum();

                // Tamamlanan egzersizleri hesapla
                var completedExercises = context.MemberExerciseProgresses
                    .Where(mep => mep.MemberID == memberId
                                  && mep.CompletedDate >= dateOnly
                                  && mep.CompletedDate < nextDay
                                  && mep.DeletedDate == null)
                    .Select(mep => mep.WorkoutProgramExerciseID)
                    .Distinct()
                    .Count();

                var completedSets = context.MemberExerciseProgresses
                    .Where(mep => mep.MemberID == memberId
                                  && mep.CompletedDate >= dateOnly
                                  && mep.CompletedDate < nextDay
                                  && mep.DeletedDate == null)
                    .Sum(mep => mep.CompletedSets);

                var completionPercentage = totalExercises > 0 ? (double)completedExercises / totalExercises * 100 : 0;

                return new DailyWorkoutProgressDto
                {
                    WorkoutDate = date,
                    TotalExercises = totalExercises,
                    CompletedExercises = completedExercises,
                    CompletionPercentage = Math.Round(completionPercentage, 2),
                    TotalSets = totalSets,
                    CompletedSets = completedSets
                };
            }
        }

        public List<DailyWorkoutProgressDto> GetWeeklyWorkoutProgress(int memberId, DateTime startDate)
        {
            var result = new List<DailyWorkoutProgressDto>();
            for (int i = 0; i < 7; i++)
            {
                var date = startDate.AddDays(i);
                result.Add(GetDailyWorkoutProgress(memberId, date));
            }
            return result;
        }

        public List<DailyWorkoutProgressDto> GetMonthlyWorkoutProgress(int memberId, int year, int month)
        {
            var result = new List<DailyWorkoutProgressDto>();
            var daysInMonth = DateTime.DaysInMonth(year, month);

            for (int day = 1; day <= daysInMonth; day++)
            {
                var date = new DateTime(year, month, day);
                result.Add(GetDailyWorkoutProgress(memberId, date));
            }
            return result;
        }

        public List<ExerciseCompletionStatusDto> GetProgramExerciseCompletionStatus(int memberId, int memberWorkoutProgramId, DateTime date)
        {
            using (GymContext context = new GymContext())
            {
                var dateOnly = date.Date;
                var nextDay = dateOnly.AddDays(1);

                // Program egzersizlerini al
                var programExercises = (from mwp in context.MemberWorkoutPrograms
                                       join wpt in context.WorkoutProgramTemplates on mwp.WorkoutProgramTemplateID equals wpt.WorkoutProgramTemplateID
                                       join wpd in context.WorkoutProgramDays on wpt.WorkoutProgramTemplateID equals wpd.WorkoutProgramTemplateID
                                       join wpe in context.WorkoutProgramExercises on wpd.WorkoutProgramDayID equals wpe.WorkoutProgramDayID
                                       where mwp.MemberWorkoutProgramID == memberWorkoutProgramId
                                       select wpe.WorkoutProgramExerciseID).ToList();

                // Tamamlanan egzersizleri al
                var completedExercises = context.MemberExerciseProgresses
                    .Where(mep => mep.MemberID == memberId
                                  && mep.MemberWorkoutProgramID == memberWorkoutProgramId
                                  && mep.CompletedDate >= dateOnly
                                  && mep.CompletedDate < nextDay
                                  && mep.DeletedDate == null)
                    .ToList();

                var result = new List<ExerciseCompletionStatusDto>();

                foreach (var exerciseId in programExercises)
                {
                    var progress = completedExercises.FirstOrDefault(ce => ce.WorkoutProgramExerciseID == exerciseId);

                    result.Add(new ExerciseCompletionStatusDto
                    {
                        WorkoutProgramExerciseID = exerciseId,
                        IsCompleted = progress != null,
                        CompletedDate = progress?.CompletedDate,
                        CompletedSets = progress?.CompletedSets,
                        ActualReps = progress?.ActualReps,
                        Notes = progress?.Notes
                    });
                }

                return result;
            }
        }

        public bool IsExerciseCompletedToday(int memberId, int workoutProgramExerciseId, DateTime date)
        {
            using (GymContext context = new GymContext())
            {
                var dateOnly = date.Date;
                var nextDay = dateOnly.AddDays(1);

                return context.MemberExerciseProgresses
                    .Any(mep => mep.MemberID == memberId
                                && mep.WorkoutProgramExerciseID == workoutProgramExerciseId
                                && mep.CompletedDate >= dateOnly
                                && mep.CompletedDate < nextDay
                                && mep.DeletedDate == null);
            }
        }

        public DateTime? GetLastWorkoutDate(int memberId)
        {
            using (GymContext context = new GymContext())
            {
                return context.MemberExerciseProgresses
                    .Where(mep => mep.MemberID == memberId && mep.DeletedDate == null)
                    .Max(mep => (DateTime?)mep.CompletedDate);
            }
        }

        public List<MemberExerciseProgressStatsDto> GetCompanyProgressStats(int companyId, DateTime startDate, DateTime endDate)
        {
            using (GymContext context = new GymContext())
            {
                var result = from mep in context.MemberExerciseProgresses
                             join m in context.Members on mep.MemberID equals m.MemberID
                             where mep.CompanyID == companyId
                                   && mep.CompletedDate >= startDate
                                   && mep.CompletedDate <= endDate
                                   && mep.DeletedDate == null
                             group mep by new { mep.MemberID, m.Name } into g
                             select new MemberExerciseProgressStatsDto
                             {
                                 MemberID = g.Key.MemberID,
                                 MemberName = g.Key.Name,
                                 TotalCompletedExercises = g.Count(),
                                 TotalCompletedSets = g.Sum(x => x.CompletedSets),
                                 LastWorkoutDate = g.Max(x => x.CompletedDate),
                                 WeeklyCompletedExercises = 0, // Bu hesaplama ayrı yapılacak
                                 MonthlyCompletedExercises = 0, // Bu hesaplama ayrı yapılacak
                                 CompletionPercentage = 0 // Bu hesaplama ayrı yapılacak
                             };

                return result.OrderByDescending(x => x.TotalCompletedExercises).ToList();
            }
        }

        public List<MemberExerciseProgressStatsDto> GetTopActiveMembers(int companyId, int topCount = 10, DateTime? startDate = null)
        {
            using (GymContext context = new GymContext())
            {
                var query = from mep in context.MemberExerciseProgresses
                           join m in context.Members on mep.MemberID equals m.MemberID
                           where mep.CompanyID == companyId && mep.DeletedDate == null
                           select new { mep, m };

                if (startDate.HasValue)
                {
                    query = query.Where(x => x.mep.CompletedDate >= startDate.Value);
                }

                var result = query.GroupBy(x => new { x.mep.MemberID, x.m.Name })
                                 .Select(g => new MemberExerciseProgressStatsDto
                                 {
                                     MemberID = g.Key.MemberID,
                                     MemberName = g.Key.Name,
                                     TotalCompletedExercises = g.Count(),
                                     TotalCompletedSets = g.Sum(x => x.mep.CompletedSets),
                                     LastWorkoutDate = g.Max(x => x.mep.CompletedDate),
                                     WeeklyCompletedExercises = 0,
                                     MonthlyCompletedExercises = 0,
                                     CompletionPercentage = 0
                                 })
                                 .OrderByDescending(x => x.TotalCompletedExercises)
                                 .Take(topCount);

                return result.ToList();
            }
        }

        public int GetTotalCompletedExercisesCount(int companyId, DateTime startDate, DateTime endDate)
        {
            using (GymContext context = new GymContext())
            {
                return context.MemberExerciseProgresses
                    .Where(mep => mep.CompanyID == companyId
                                  && mep.CompletedDate >= startDate
                                  && mep.CompletedDate <= endDate
                                  && mep.DeletedDate == null)
                    .Count();
            }
        }

        public DateTime? GetLastProgressTime(int memberId)
        {
            using (GymContext context = new GymContext())
            {
                return context.MemberExerciseProgresses
                    .Where(mep => mep.MemberID == memberId && mep.DeletedDate == null)
                    .Max(mep => (DateTime?)mep.CreationDate);
            }
        }
    }
}
